{"name": "ruoyi", "version": "3.6.4", "description": "Ai•<PERSON><PERSON>大模型", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@antv/g6": "^4.8.0", "@braks/revue-draggable": "^0.4.3", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue/composition-api": "^1.7.2", "axios": "^0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "devtools-detector": "^2.0.23", "echarts": "^5.6.0", "element-ui": "2.15.14", "encryptlong": "^3.1.4", "event-source-polyfill": "^1.0.31", "eventsource-polyfill": "^0.9.6", "file-saver": "^2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html-docx-js": "^0.3.1", "js-base64": "^3.7.7", "js-beautify": "1.13.0", "js-cookie": "^3.0.1", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mammoth": "^1.8.0", "marked": "^4.3.0", "monaco-editor": "^0.30.1", "monaco-editor-webpack-plugin": "^6.0.0", "nprogress": "0.2.0", "qs": "^6.13.0", "quill": "^1.3.7", "recorder-core": "^1.3.24102001", "screenfull": "5.0.2", "socket.io-client": "^4.7.5", "sortablejs": "1.10.2", "uuid": "^3.4.0", "vditor": "^3.11.1", "vue": "2.6.12", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.10", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-socket.io": "^3.0.10", "vuedraggable": "2.24.3", "vuex": "3.6.0", "worker-loader": "^2.0.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "crypto-js": "^4.2.0", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^4.5.2", "lint-staged": "10.5.3", "mini-css-extract-plugin": "^1.6.2", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "webpack": "^4.47.0", "webpack-cli": "^4.10.0", "webpack-subresource-integrity": "^1.5.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}