<template>
  <div class="app-container">
    <el-card shadow="never">
      <!-- 课程信息 -->
      <div class="course-header">
        <h3>{{ course.title }}</h3>
        <p>{{ course.description }}</p>
      </div>

      <!-- 搜索和操作区域 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索用户姓名或账号"
          style="width: 300px"
          clearable
          @keyup.enter.native="getList"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="getList"
        >搜索</el-button>
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="showAddUserDialog"
        >添加用户</el-button>
      </div>

      <!-- 用户表格 -->
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="用户ID" prop="userId" width="90" align="center" />
        <el-table-column label="头像" width="90" align="center">
          <template slot-scope="{row}">
            <el-avatar :size="40" :src="row.avatar" fit="cover" />
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="realName" />
        <el-table-column label="用户名" prop="username" />
        <el-table-column label="邮箱" prop="email" width="180" />
        <el-table-column label="加入时间" width="160" align="center">
          <template slot-scope="{row}">
            <span>{{ parseTime(row.joinTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120" align="center">
          <template slot-scope="{row}">
            <el-tag
              :type="row.isHidden ? 'info' : 'success'"
              size="small"
            >{{ row.isHidden ? '隐身' : '正常' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="200"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="{row}">
            <el-button
              size="mini"
              type="text"
              @click="toggleUserVisibility(row)"
            >{{ row.isHidden ? '取消隐身' : '设为隐身' }}</el-button>
            <el-button
              size="mini"
              type="text"
              style="color: #f56c6c"
              @click="handleRemove(row)"
            >移除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.pageNum"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加用户对话框 -->
    <el-dialog
      title="添加用户到课程"
      :visible.sync="addUserDialogVisible"
      width="600px"
      append-to-body
    >
      <el-form
        ref="addUserForm"
        :model="addUserForm"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="搜索用户">
          <el-input
            v-model="addUserForm.searchKey"
            placeholder="输入用户名或邮箱"
            clearable
            @keyup.enter.native="searchUsers"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="searchUsers"
          >搜索</el-button>
        </el-form-item>
        <el-form-item label="选择用户">
          <el-table
            :data="userSearchResults"
            border
            fit
            highlight-current-row
            @selection-change="handleUserSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="头像" width="70" align="center">
              <template slot-scope="{row}">
                <el-avatar :size="32" :src="row.avatar" fit="cover" />
              </template>
            </el-table-column>
            <el-table-column label="用户名" prop="username" />
            <el-table-column label="姓名" prop="realName" />
            <el-table-column label="邮箱" prop="email" width="180" />
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addUserDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmAddUsers"
          :disabled="selectedUsers.length === 0"
        >确认添加</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  // import { getCourseUsers, addUsersToCourse, removeUserFromCourse, updateUserVisibility } from '@/api/course'
  // import { searchUsers } from '@/api/system/user'
  import Pagination from '@/components/Pagination'

  export default {
    name: 'CourseUsers',
    components: { Pagination },
    data() {
      return {
        // 课程信息
        course: {
          id: this.$route.params.courseId,
          title: '高级Python编程',
          description: '本课程将深入讲解Python高级特性和最佳实践'
        },
        // 用户列表
        list: [],
        total: 0,
        listLoading: true,
        listQuery: {
          pageNum: 1,
          pageSize: 10,
          keyword: ''
        },
        // 添加用户相关
        addUserDialogVisible: false,
        addUserForm: {
          searchKey: ''
        },
        userSearchResults: [],
        selectedUsers: [],
        // 模拟数据
        mockUsers: [
          {
            userId: 1,
            username: 'student1',
            realName: '张三',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>',
            joinTime: '2023-05-10 09:30:00',
            isHidden: false
          },
          {
            userId: 2,
            username: 'student2',
            realName: '李四',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>',
            joinTime: '2023-05-11 14:15:00',
            isHidden: true
          },
          {
            userId: 3,
            username: 'student3',
            realName: '王五',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>',
            joinTime: '2023-05-12 10:45:00',
            isHidden: false
          },
          {
            userId: 4,
            username: 'student4',
            realName: '赵六',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>',
            joinTime: '2023-05-13 16:20:00',
            isHidden: false
          },
          {
            userId: 5,
            username: 'student5',
            realName: '钱七',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>',
            joinTime: '2023-05-14 11:10:00',
            isHidden: false
          }
        ],
        mockAllUsers: [
          {
            userId: 6,
            username: 'teacher1',
            realName: '张老师',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>'
          },
          {
            userId: 7,
            username: 'teacher2',
            realName: '李教授',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>'
          },
          {
            userId: 8,
            username: 'student6',
            realName: '周八',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>'
          },
          {
            userId: 9,
            username: 'student7',
            realName: '吴九',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>'
          },
          {
            userId: 10,
            username: 'student8',
            realName: '郑十',
            avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            email: '<EMAIL>'
          }
        ]
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 获取课程用户列表 */
      getList() {
        this.listLoading = true
        // 模拟API调用
        setTimeout(() => {
          // 实际项目中应该使用API调用
          // getCourseUsers(this.course.id, this.listQuery).then(response => {
          //   this.list = response.rows
          //   this.total = response.total
          //   this.listLoading = false
          // })

          // 使用模拟数据
          let filteredData = [...this.mockUsers]

          // 模拟搜索过滤
          if (this.listQuery.keyword) {
            const keyword = this.listQuery.keyword.toLowerCase()
            filteredData = filteredData.filter(item =>
              item.username.toLowerCase().includes(keyword) ||
              item.realName.toLowerCase().includes(keyword)
            )
          }

          // 模拟分页
          const start = (this.listQuery.pageNum - 1) * this.listQuery.pageSize
          const end = start + this.listQuery.pageSize
          this.list = filteredData.slice(start, end)
          this.total = filteredData.length
          this.listLoading = false
        }, 500)
      },
      /** 显示添加用户对话框 */
      showAddUserDialog() {
        this.addUserDialogVisible = true
        this.addUserForm.searchKey = ''
        this.userSearchResults = []
        this.selectedUsers = []
      },
      /** 搜索用户 */
      searchUsers() {
        if (!this.addUserForm.searchKey.trim()) {
          this.$message.warning('请输入搜索关键词')
          return
        }

        // 模拟API调用
        setTimeout(() => {
          // 实际项目中应该使用API调用
          // searchUsers({ keyword: this.addUserForm.searchKey }).then(response => {
          //   this.userSearchResults = response.data
          // })

          // 使用模拟数据
          const keyword = this.addUserForm.searchKey.toLowerCase()
          this.userSearchResults = this.mockAllUsers.filter(user =>
            !this.mockUsers.some(cu => cu.userId === user.userId) &&  // 排除已在课程中的用户
            (user.username.toLowerCase().includes(keyword) ||
              user.realName.toLowerCase().includes(keyword) ||
              user.email.toLowerCase().includes(keyword))
          )
        }, 300)
      },
      /** 处理用户选择 */
      handleUserSelectionChange(selection) {
        this.selectedUsers = selection
      },
      /** 确认添加用户 */
      confirmAddUsers() {
        const userIds = this.selectedUsers.map(user => user.userId)

        // 模拟API调用
        setTimeout(() => {
          // 实际项目中应该使用API调用
          // addUsersToCourse(this.course.id, { userIds }).then(() => {
          //   this.$message.success('添加成功')
          //   this.addUserDialogVisible = false
          //   this.getList()
          // })

          // 使用模拟数据
          const newUsers = this.selectedUsers.map(user => ({
            ...user,
            joinTime: new Date().toISOString(),
            isHidden: false
          }))

          this.mockUsers = [...this.mockUsers, ...newUsers]
          this.$message.success('添加成功')
          this.addUserDialogVisible = false
          this.getList()
        }, 300)
      },
      /** 切换用户隐身状态 */
      toggleUserVisibility(row) {
        const newStatus = !row.isHidden
        const action = newStatus ? '隐身' : '取消隐身'

        this.$confirm(`确定要${action}用户 "${row.realName}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 模拟API调用
          setTimeout(() => {
            // 实际项目中应该使用API调用
            // updateUserVisibility(this.course.id, row.userId, newStatus).then(() => {
            //   row.isHidden = newStatus
            //   this.$message.success(`${action}成功`)
            // })

            // 使用模拟数据
            row.isHidden = newStatus
            this.$message.success(`${action}成功`)
          }, 300)
        }).catch(() => {})
      },
      /** 移除用户 */
      handleRemove(row) {
        this.$confirm(`确定要从课程中移除用户 "${row.realName}" 吗?`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 模拟API调用
          setTimeout(() => {
            // 实际项目中应该使用API调用
            // removeUserFromCourse(this.course.id, row.userId).then(() => {
            //   this.$message.success('移除成功')
            //   this.getList()
            // })

            // 使用模拟数据
            this.mockUsers = this.mockUsers.filter(user => user.userId !== row.userId)
            this.$message.success('移除成功')
            this.getList()
          }, 300)
        }).catch(() => {})
      },
      /** 时间格式化 */
      parseTime(time) {
        return time ? new Date(time).toLocaleString() : ''
      }
    }
  }
</script>

<style scoped>
  .course-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }
  .course-header h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
  }
  .course-header p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
  .filter-container {
    margin-bottom: 20px;
  }
</style>
