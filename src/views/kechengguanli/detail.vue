<template>
  <div class="app-container">
    <el-card shadow="hover" class="mb20">
      <div slot="header">
        <span>课程基本信息</span>
<!--        <el-button-->
<!--          v-if="!course.isClone"-->
<!--          type="primary"-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          style="float: right; padding: 6px 12px"-->
<!--          @click="handleEditCourse"-->
<!--          v-hasPermi="['education:course:edit']"-->
<!--        >编辑</el-button>-->
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-image
            style="width: 100%; height: 160px"
            :src="course.banner"
            fit="cover"
          ></el-image>
        </el-col>
        <el-col :span="18">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="课程标题">{{ course.title }}</el-descriptions-item>
            <el-descriptions-item label="所属专业">
              <span>{{ getMajorLabel(course.major) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="课程状态">
              <el-tag :type="course.status === '1' ? 'success' : 'info'">
                {{ course.status === '1' ? '已发布' : '未发布' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(course.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="课程简介" :span="2">{{ course.description }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">
        <span>课程单元管理</span>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          style="float: right; padding: 6px 12px"
          @click="handleAddUnit"
          v-hasPermi="['education:course:unit:add']"
        >添加单元</el-button>
      </div>

      <!-- 单元列表 -->
      <el-table
        v-loading="unitLoading"
        :data="unitList"
        row-key="id"
        border
        @selection-change="handleUnitSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="排序" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              icon="el-icon-top"
              circle
              @click="moveUp(scope.$index)"
              :disabled="scope.$index === 0"
            ></el-button>
            <el-button
              size="mini"
              icon="el-icon-bottom"
              circle
              @click="moveDown(scope.$index)"
              :disabled="scope.$index === unitList.length - 1"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column label="单元名称" prop="name" width="180" />
        <el-table-column label="单元类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="时间设置" width="220">
          <template slot-scope="scope">
            <div>开始: {{ parseTime(scope.row.startTime) }}</div>
            <div>结束: {{ parseTime(scope.row.endTime) }}</div>
            <div>时长: {{ scope.row.duration }}分钟</div>
          </template>
        </el-table-column>
        <el-table-column label="题目设置" width="220">
          <template slot-scope="scope">
            <div class="publish-setting-item">
              <span class="setting-label">答案公布:</span>
              <el-tag :type="getPublishTagType(scope.row.answerPublish)" size="small">
                {{ getPublishLabel(scope.row.answerPublish, answerPublishOptions) }}
              </el-tag>
            </div>
            <div class="publish-setting-item">
              <span class="setting-label">解析公布:</span>
              <el-tag :type="getPublishTagType(scope.row.analysisPublish)" size="small">
                {{ getPublishLabel(scope.row.analysisPublish, analysisPublishOptions) }}
              </el-tag>
            </div>
            <div class="publish-setting-item">
              <span class="setting-label">分数公布:</span>
              <el-tag :type="getPublishTagType(scope.row.scorePublish)" size="small">
                {{ getPublishLabel(scope.row.scorePublish, scorePublishOptions) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="工具配置" width="180">
          <template slot-scope="scope">
            <el-tag
              v-for="tool in scope.row.tools"
              :key="tool"
              size="mini"
              style="margin-right: 5px; margin-bottom: 5px"
            >{{ getToolName(tool) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEditUnit(scope.row)"
              v-hasPermi="['education:course:unit:edit']"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteUnit(scope.row)"
              v-hasPermi="['education:course:unit:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改课程单元对话框 -->
    <el-dialog :title="unitTitle" :visible.sync="unitOpen" width="900px" append-to-body>
      <el-form ref="unitForm" :model="unitForm" :rules="unitRules" label-width="100px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单元名称" prop="name">
                  <el-input v-model="unitForm.name" placeholder="请输入单元名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单元类型" prop="type">
                  <el-select v-model="unitForm.type" placeholder="请选择单元类型">
                    <el-option
                      v-for="item in unitTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startTime">
                  <el-date-picker
                    v-model="unitForm.startTime"
                    type="datetime"
                    placeholder="选择开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker
                    v-model="unitForm.endTime"
                    type="datetime"
                    placeholder="选择结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单元时长(分钟)" prop="duration">
                  <el-input-number v-model="unitForm.duration" :min="1" :max="600" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单元状态" prop="status">
                  <el-radio-group v-model="unitForm.status">
                    <el-radio
                      v-for="dict in statusOptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="题目设置" name="question">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="答案公布时间" prop="answerPublish">
                  <el-select v-model="unitForm.answerPublish" placeholder="请选择答案公布时间">
                    <el-option
                      v-for="item in answerPublishOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="解析公布时间" prop="analysisPublish">
                  <el-select v-model="unitForm.analysisPublish" placeholder="请选择解析公布时间">
                    <el-option
                      v-for="item in analysisPublishOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分数公布时间" prop="scorePublish">
                  <el-select v-model="unitForm.scorePublish" placeholder="请选择分数公布时间">
                    <el-option
                      v-for="item in scorePublishOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="unitForm.hasPython">
                <el-form-item label="Python题设置">
                  <el-col :span="8">
                    <el-form-item label="最大提交次数" prop="maxSubmitTimes">
                      <el-input-number v-model="unitForm.maxSubmitTimes" :min="1" :max="100" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最大运行次数" prop="maxRunTimes">
                      <el-input-number v-model="unitForm.maxRunTimes" :min="1" :max="1000" />
                    </el-form-item>
                  </el-col>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="工具配置" name="tools">
            <el-form-item label="选择工具">
              <el-checkbox-group v-model="unitForm.tools">
                <el-checkbox
                  v-for="item in toolOptions"
                  :key="item.value"
                  :label="item.value"
                >{{item.label}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="教学资源" name="resources">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddResource">添加资源</el-button>
            <el-button type="primary" icon="el-icon-folder-opened" @click="handleSelectFromLibrary">从资源库选择</el-button>

            <el-table :data="unitForm.resources" border style="width: 100%; margin-top: 15px">
              <el-table-column prop="name" label="资源名称" width="180" />
              <el-table-column prop="type" label="资源类型" width="120">
                <template slot-scope="scope">
                  <dict-tag :options="resourceTypeOptions" :value="scope.row.type"/>
                </template>
              </el-table-column>
              <el-table-column prop="url" label="资源链接" show-overflow-tooltip />
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDeleteResource(scope.$index)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="试题管理" name="questions">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddQuestion">添加试题</el-button>
            <el-button type="primary" icon="el-icon-folder-opened" @click="handleSelectQuestionFromLibrary">从题库选择</el-button>

            <el-table :data="unitForm.questions" border style="width: 100%; margin-top: 15px">
              <el-table-column prop="title" label="题目" width="180" />
              <el-table-column prop="type" label="题型" width="120">
                <template slot-scope="scope">
                  <dict-tag :options="questionTypeOptions" :value="scope.row.type"/>
                </template>
              </el-table-column>
              <el-table-column prop="difficulty" label="难度" width="100">
                <template slot-scope="scope">
                  <dict-tag :options="difficultyOptions" :value="scope.row.difficulty"/>
                </template>
              </el-table-column>
              <el-table-column prop="score" label="分值" width="80" />
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDeleteQuestion(scope.$index)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUnitForm">确 定</el-button>
        <el-button @click="cancelUnitForm">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加资源对话框 -->
    <el-dialog title="添加教学资源" :visible.sync="resourceOpen" width="600px" append-to-body>
      <el-form ref="resourceForm" :model="resourceForm" :rules="resourceRules" label-width="100px">
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="resourceForm.name" placeholder="请输入资源名称" />
        </el-form-item>
        <el-form-item label="资源类型" prop="type">
          <el-select v-model="resourceForm.type" placeholder="请选择资源类型">
            <el-option
              v-for="item in resourceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资源文件" prop="url">
          <file-upload v-model="resourceForm.url" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResourceForm">确 定</el-button>
        <el-button @click="cancelResourceForm">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加试题对话框 -->
    <el-dialog title="添加试题" :visible.sync="questionOpen" width="800px" append-to-body>
      <el-form ref="questionForm" :model="questionForm" :rules="questionRules" label-width="100px">
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="questionForm.type" placeholder="请选择题型" @change="handleQuestionTypeChange">
            <el-option
              v-for="item in questionTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题目" prop="title">
          <el-input v-model="questionForm.title" type="textarea" :rows="3" placeholder="请输入题目内容" />
        </el-form-item>
        <el-form-item label="选项" prop="options" v-if="questionForm.type === 'single' || questionForm.type === 'multiple'">
          <el-form-item
            v-for="(option, index) in questionForm.options"
            :key="index"
            :prop="'options.' + index + '.content'"
            :rules="{ required: true, message: '选项内容不能为空', trigger: 'blur' }"
          >
            <el-input v-model="option.content" style="width: 80%; margin-bottom: 10px">
              <template slot="prepend">选项{{ String.fromCharCode(65 + index) }}</template>
              <el-button slot="append" icon="el-icon-delete" @click="removeOption(index)"></el-button>
            </el-input>
          </el-form-item>
          <el-button @click="addOption">添加选项</el-button>
        </el-form-item>
        <el-form-item label="正确答案" prop="answer">
          <template v-if="questionForm.type === 'single' || questionForm.type === 'multiple'">
            <el-checkbox-group v-model="questionForm.answer" :max="questionForm.type === 'single' ? 1 : 10">
              <el-checkbox
                v-for="(option, index) in questionForm.options"
                :key="index"
                :label="String.fromCharCode(65 + index)"
              >{{ String.fromCharCode(65 + index) }}</el-checkbox>
            </el-checkbox-group>
          </template>
          <el-input
            v-else
            v-model="questionForm.answer"
            type="textarea"
            :rows="3"
            placeholder="请输入正确答案"
          />
        </el-form-item>
        <el-form-item label="解析" prop="analysis">
          <el-input v-model="questionForm.analysis" type="textarea" :rows="3" placeholder="请输入题目解析" />
        </el-form-item>
        <el-form-item label="难度" prop="difficulty">
          <el-rate
            v-model="questionForm.difficulty"
            :max="5"
            :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
          />
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input-number v-model="questionForm.score" :min="1" :max="100" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitQuestionForm">确 定</el-button>
        <el-button @click="cancelQuestionForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  // import { getCourse, updateCourse } from "@/api/education/course";
  // import { listUnit, getUnit, addUnit, updateUnit, deleteUnit, sortUnit } from "@/api/education/unit";
  import FileUpload from '@/components/FileUpload';

  export default {
    name: "CourseDetail",
    components: { FileUpload },
    data() {
      return {
        // 课程ID
        courseId: undefined,
        course: null,
        // 课程信息
        courses: {
          1: {
            id: 1,
            title: "Python编程基础",
            description: "本课程将带领学员从零开始学习Python编程语言，掌握基础语法和常用数据结构。",
            major: "computer",
            banner: require("@/assets/ys/python编程基础banner.jpg"),
            thumbnail: require("@/assets/ys/python编程基础.jpg"),
            unitCount: 12,
            status: "1",
            createTime: "2023-05-10 14:30:22",
            isClone: false
          },
          2: {
            id: 2,
            title: "机器学习入门",
            description: "介绍机器学习的基本概念和常用算法，包括监督学习和无监督学习。",
            major: "ai",
            banner: require("@/assets/ys/机器学习banner.jpg"),
            thumbnail: require("@/assets/ys/机器学习入门.jpg"),
            unitCount: 8,
            status: "1",
            createTime: "2023-06-15 09:12:45",
            isClone: false
          },
          3: {
            id: 3,
            title: "Web前端开发",
            description: "学习HTML、CSS和JavaScript等前端开发技术，构建响应式网站。",
            major: "computer",
            banner: require("@/assets/ys/web前端开发benner.jpg"),
            thumbnail: require("@/assets/ys/web前端.jpg"),
            unitCount: 10,
            status: "0",
            createTime: "2023-07-20 16:45:33",
            isClone: true
          },
          4: {
            id: 4,
            title: "数据分析实战",
            description: "使用Python进行数据清洗、分析和可视化，掌握Pandas和Matplotlib等工具。",
            major: "data",
            banner: require("@/assets/ys/数据分析实战banner.jpg"),
            thumbnail: require("@/assets/ys/数据分析实战.jpg"),
            unitCount: 6,
            status: "1",
            createTime: "2023-08-05 11:20:18",
            isClone: false
          }
        },

        // 单元加载状态
        unitLoading: false,

        unitList: null,
        // 单元列表数据
        unitList1: [
          {
            id: 101,
            name: "Python环境搭建",
            type: "video",
            startTime: "2023-09-01 08:00:00",
            endTime: "2023-09-07 23:59:59",
            duration: 60,
            answerPublish: "after_submit",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "material_lib"],
            resources: [
              { id: 1001, name: "Python安装指南", type: "doc", url: "https://example.com/resources/install.pdf" },
              { id: 1002, name: "开发环境配置视频", type: "video", url: "https://example.com/resources/setup.mp4" }
            ],
            questions: [
              { id: 2001, title: "Python解释器是什么？", type: "single", difficulty: "1", score: 5 },
              { id: 2002, title: "如何安装第三方库？", type: "short", difficulty: "2", score: 10 }
            ],
            hasPython: false
          },
          {
            id: 102,
            name: "Python基础语法",
            type: "text",
            startTime: "2023-09-08 08:00:00",
            endTime: "2023-09-14 23:59:59",
            duration: 120,
            answerPublish: "after_review",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "ai_lab"],
            resources: [
              { id: 1003, name: "Python语法速查表", type: "doc", url: "https://example.com/resources/cheatsheet.pdf" }
            ],
            questions: [
              { id: 2003, title: "Python变量命名规则", type: "single", difficulty: "1", score: 5 },
              { id: 2004, title: "Python数据类型有哪些？", type: "multiple", difficulty: "2", score: 10 },
              { id: 2005, title: "编写一个计算圆面积的函数", type: "program", difficulty: "3", score: 20 }
            ],
            hasPython: true,
            maxSubmitTimes: 5,
            maxRunTimes: 20
          },
          {
            id: 103,
            name: "Python流程控制",
            type: "practice",
            startTime: "2023-09-15 08:00:00",
            endTime: "2023-09-21 23:59:59",
            duration: 180,
            answerPublish: "after_end",
            analysisPublish: "after_end",
            scorePublish: "after_end",
            status: "0",
            tools: ["ai_assistant", "virtual_machine"],
            resources: [
              { id: 1004, name: "流程控制练习题", type: "doc", url: "https://example.com/resources/control.pdf" },
              { id: 1005, name: "示例代码", type: "code", url: "https://example.com/resources/examples.zip" }
            ],
            questions: [
              { id: 2006, title: "if-else语句的使用场景", type: "short", difficulty: "2", score: 10 },
              { id: 2007, title: "编写一个猜数字游戏", type: "program", difficulty: "4", score: 30 }
            ],
            hasPython: true,
            maxSubmitTimes: 3,
            maxRunTimes: 15
          }
        ],
        unitList2: [
          {
            id: 201,
            name: "机器学习概述",
            type: "video",
            startTime: "2023-10-01 08:00:00",
            endTime: "2023-10-07 23:59:59",
            duration: 90,
            answerPublish: "after_submit",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "ai_lab"],
            resources: [
              { id: 2001, name: "机器学习发展史", type: "doc", url: "https://example.com/ml/history.pdf" },
              { id: 2002, name: "基础概念讲解视频", type: "video", url: "https://example.com/ml/concepts.mp4" }
            ],
            questions: [
              { id: 3001, title: "什么是监督学习？", type: "single", difficulty: "1", score: 5 },
              { id: 3002, title: "机器学习的三大类型", type: "multiple", difficulty: "2", score: 10 }
            ],
            hasPython: false
          },
          {
            id: 202,
            name: "线性回归实践",
            type: "practice",
            startTime: "2023-10-08 08:00:00",
            endTime: "2023-10-14 23:59:59",
            duration: 180,
            answerPublish: "after_review",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "virtual_machine", "toolkit"],
            resources: [
              { id: 2003, name: "Scikit-learn使用指南", type: "doc", url: "https://example.com/ml/sklearn.pdf" },
              { id: 2004, name: "房价数据集", type: "dataset", url: "https://example.com/ml/housing.csv" }
            ],
            questions: [
              { id: 3003, title: "线性回归的损失函数", type: "short", difficulty: "3", score: 15 },
              { id: 3004, title: "实现波士顿房价预测", type: "program", difficulty: "4", score: 30 }
            ],
            hasPython: true,
            maxSubmitTimes: 5,
            maxRunTimes: 25
          },
          {
            id: 203,
            name: "分类算法",
            type: "text",
            startTime: "2023-10-15 08:00:00",
            endTime: "2023-10-21 23:59:59",
            duration: 150,
            answerPublish: "after_end",
            analysisPublish: "after_end",
            scorePublish: "after_end",
            status: "0",
            tools: ["ai_assistant", "ai_lab"],
            resources: [
              { id: 2005, name: "逻辑回归原理", type: "doc", url: "https://example.com/ml/logistic.pdf" },
              { id: 2006, name: "鸢尾花数据集", type: "dataset", url: "https://example.com/ml/iris.csv" }
            ],
            questions: [
              { id: 3005, title: "SVM的核心思想", type: "short", difficulty: "3", score: 15 },
              { id: 3006, title: "实现手写数字识别", type: "program", difficulty: "5", score: 40 }
            ],
            hasPython: true,
            maxSubmitTimes: 4,
            maxRunTimes: 20
          }
        ],

        unitList3: [
          {
            id: 301,
            name: "HTML5基础",
            type: "video",
            startTime: "2023-11-01 08:00:00",
            endTime: "2023-11-07 23:59:59",
            duration: 80,
            answerPublish: "after_submit",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["material_lib", "virtual_machine"],
            resources: [
              { id: 3001, name: "HTML5标签手册", type: "doc", url: "https://example.com/web/html5.pdf" },
              { id: 3002, name: "基础布局教学", type: "video", url: "https://example.com/web/layout.mp4" }
            ],
            questions: [
              { id: 4001, title: "HTML5新特性", type: "multiple", difficulty: "2", score: 10 },
              { id: 4002, title: "构建个人简介页面", type: "program", difficulty: "3", score: 20 }
            ],
            hasPython: false
          },
          {
            id: 302,
            name: "CSS3样式设计",
            type: "practice",
            startTime: "2023-11-08 08:00:00",
            endTime: "2023-11-14 23:59:59",
            duration: 120,
            answerPublish: "after_review",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["material_lib", "pose_editor"],
            resources: [
              { id: 3003, name: "CSS3动画指南", type: "doc", url: "https://example.com/web/css3.pdf" },
              { id: 3004, name: "Flex布局示例", type: "code", url: "https://example.com/web/flex.zip" }
            ],
            questions: [
              { id: 4003, title: "BFC是什么？", type: "short", difficulty: "3", score: 15 },
              { id: 4004, title: "实现响应式导航栏", type: "program", difficulty: "4", score: 30 }
            ],
            hasPython: false
          },
          {
            id: 303,
            name: "JavaScript交互",
            type: "text",
            startTime: "2023-11-15 08:00:00",
            endTime: "2023-11-21 23:59:59",
            duration: 180,
            answerPublish: "after_end",
            analysisPublish: "after_end",
            scorePublish: "after_end",
            status: "0",
            tools: ["virtual_machine", "toolkit"],
            resources: [
              { id: 3005, name: "ES6特性速查", type: "doc", url: "https://example.com/web/es6.pdf" },
              { id: 3006, name: "DOM操作示例", type: "code", url: "https://example.com/web/dom.zip" }
            ],
            questions: [
              { id: 4005, title: "闭包的概念", type: "short", difficulty: "4", score: 20 },
              { id: 4006, title: "实现购物车功能", type: "program", difficulty: "5", score: 40 }
            ],
            hasPython: false
          }
        ],

        unitList4: [
          {
            id: 401,
            name: "Pandas基础",
            type: "video",
            startTime: "2023-12-01 08:00:00",
            endTime: "2023-12-07 23:59:59",
            duration: 90,
            answerPublish: "after_submit",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "virtual_machine"],
            resources: [
              { id: 4001, name: "Pandas速查表", type: "doc", url: "https://example.com/da/pandas.pdf" },
              { id: 4002, name: "数据清洗演示", type: "video", url: "https://example.com/da/cleaning.mp4" }
            ],
            questions: [
              { id: 5001, title: "Series和DataFrame区别", type: "short", difficulty: "2", score: 10 },
              { id: 5002, title: "数据去重操作", type: "program", difficulty: "3", score: 20 }
            ],
            hasPython: true,
            maxSubmitTimes: 5,
            maxRunTimes: 20
          },
          {
            id: 402,
            name: "数据可视化",
            type: "practice",
            startTime: "2023-12-08 08:00:00",
            endTime: "2023-12-14 23:59:59",
            duration: 120,
            answerPublish: "after_review",
            analysisPublish: "after_review",
            scorePublish: "after_review",
            status: "1",
            tools: ["ai_assistant", "ai_image"],
            resources: [
              { id: 4003, name: "Matplotlib指南", type: "doc", url: "https://example.com/da/matplotlib.pdf" },
              { id: 4004, name: "销售数据集", type: "dataset", url: "https://example.com/da/sales.csv" }
            ],
            questions: [
              { id: 5003, title: "折线图和柱状图适用场景", type: "short", difficulty: "3", score: 15 },
              { id: 5004, title: "绘制销售趋势图", type: "program", difficulty: "4", score: 30 }
            ],
            hasPython: true,
            maxSubmitTimes: 4,
            maxRunTimes: 15
          },
          {
            id: 403,
            name: "实战项目",
            type: "project",
            startTime: "2023-12-15 08:00:00",
            endTime: "2023-12-21 23:59:59",
            duration: 240,
            answerPublish: "after_end",
            analysisPublish: "after_end",
            scorePublish: "after_end",
            status: "0",
            tools: ["ai_assistant", "toolkit", "material_lib"],
            resources: [
              { id: 4005, name: "电商用户行为数据", type: "dataset", url: "https://example.com/da/ecommerce.csv" },
              { id: 4006, name: "分析报告模板", type: "doc", url: "https://example.com/da/template.pdf" }
            ],
            questions: [
              { id: 5005, title: "用户购买行为分析", type: "program", difficulty: "5", score: 50 }
            ],
            hasPython: true,
            maxSubmitTimes: 3,
            maxRunTimes: 10
          }
        ],
        // 状态数据字典
        statusOptions: [
          { value: "0", label: "未发布" },
          { value: "1", label: "已发布" }
        ],
        // 专业数据字典
        majorOptions: [
          { value: "computer", label: "计算机" },
          { value: "ai", label: "人工智能" },
          { value: "data", label: "数据科学" },
          { value: "math", label: "数学" },
          { value: "physics", label: "物理" }
        ],
        // 单元类型数据字典
        unitTypeOptions: [
          { value: "video", label: "视频课程" },
          { value: "text", label: "图文课程" },
          { value: "practice", label: "实践练习" },
          { value: "exam", label: "测验考试" }
        ],
        // 答案公布时间选项
        answerPublishOptions: [
          { value: "never", label: "不公布" },
          { value: "one_by_one", label: "逐题公布" },
          { value: "after_submit", label: "交卷后公布" },
          { value: "after_review", label: "批改后公布" },
          { value: "after_end", label: "结束后公布" }
        ],
        // 解析公布时间选项
        analysisPublishOptions: [
          { value: "never", label: "不公布" },
          { value: "one_by_one", label: "逐题公布" },
          { value: "after_submit", label: "交卷后公布" },
          { value: "after_review", label: "批改后公布" },
          { value: "after_end", label: "结束后公布" }
        ],
        // 分数公布时间选项
        scorePublishOptions: [
          { value: "never", label: "不公布" },
          { value: "after_submit", label: "交卷后公布" },
          { value: "after_review", label: "批改后公布" },
          { value: "after_end", label: "结束后公布" }
        ],
        // 工具选项
        toolOptions: [
          { value: "ai_assistant", label: "AI助手" },
          { value: "ai_image", label: "AI生图" },
          { value: "ai_lab", label: "AI对话实验室" },
          { value: "virtual_machine", label: "虚拟机" },
          { value: "toolkit", label: "工具包" },
          { value: "material_lib", label: "素材库" },
          { value: "pose_editor", label: "姿态编辑器" }
        ],
        // 资源类型选项
        resourceTypeOptions: [
          { value: "doc", label: "文档" },
          { value: "video", label: "视频" },
          { value: "audio", label: "音频" },
          { value: "image", label: "图片" },
          { value: "code", label: "代码" },
          { value: "link", label: "链接" }
        ],
        // 题目类型选项
        questionTypeOptions: [
          { value: "single", label: "单选题" },
          { value: "multiple", label: "多选题" },
          { value: "judge", label: "判断题" },
          { value: "fill", label: "填空题" },
          { value: "short", label: "简答题" },
          { value: "program", label: "编程题" }
        ],
        // 难度选项
        difficultyOptions: [
          { value: "1", label: "简单" },
          { value: "2", label: "中等" },
          { value: "3", label: "较难" },
          { value: "4", label: "困难" },
          { value: "5", label: "极难" }
        ],
        // 单元表单参数
        unitForm: {
          id: undefined,
          name: undefined,
          type: undefined,
          startTime: undefined,
          endTime: undefined,
          duration: 60,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "0",
          tools: [],
          resources: [],
          questions: [],
          hasPython: false,
          maxSubmitTimes: 3,
          maxRunTimes: 10
        },
        // 单元表单校验
        unitRules: {
          name: [
            { required: true, message: "单元名称不能为空", trigger: "blur" }
          ],
          type: [
            { required: true, message: "单元类型不能为空", trigger: "change" }
          ],
          startTime: [
            { required: true, message: "开始时间不能为空", trigger: "change" }
          ],
          endTime: [
            { required: true, message: "结束时间不能为空", trigger: "change" }
          ],
          duration: [
            { required: true, message: "单元时长不能为空", trigger: "blur" }
          ]
        },
        // 资源表单参数
        resourceForm: {
          name: undefined,
          type: undefined,
          url: undefined
        },
        // 资源表单校验
        resourceRules: {
          name: [
            { required: true, message: "资源名称不能为空", trigger: "blur" }
          ],
          type: [
            { required: true, message: "资源类型不能为空", trigger: "change" }
          ],
          url: [
            { required: true, message: "请上传资源文件", trigger: "change" }
          ]
        },
        // 试题表单参数
        questionForm: {
          type: "single",
          title: undefined,
          options: [
            { content: undefined }
          ],
          answer: [],
          analysis: undefined,
          difficulty: "1",
          score: 5
        },
        // 试题表单校验
        questionRules: {
          title: [
            { required: true, message: "题目内容不能为空", trigger: "blur" }
          ],
          answer: [
            { required: true, message: "请设置正确答案", trigger: "change" }
          ],
          score: [
            { required: true, message: "请设置题目分值", trigger: "blur" }
          ]
        },
        // 单元对话框标题
        unitTitle: "",
        // 是否显示单元对话框
        unitOpen: false,
        // 是否显示资源对话框
        resourceOpen: false,
        // 是否显示试题对话框
        questionOpen: false,
        // 当前激活的标签页
        activeTab: "basic"
      };
    },
    created() {
      this.courseId = this.$route.params.id;
      this.getCourse();
      this.getUnitList();
    },

    methods: {
      /** 获取课程详情 */
      getCourse() {
        // 实际项目中应该使用API调用
        // getCourse(this.courseId).then(response => {
        //   this.course = response.data;
        // });
        const courseId = this.$route.params.id
        this.course = this.courses[courseId] || null
      },
      getTypeLabel(type) {
        const option = this.unitTypeOptions.find(opt => opt.value === type);
        return option ? option.label : type;
      },
      getTagType(type) {
        const map = {
          text: '',
          video: 'success',
          practice: 'warning'
        };
        return map[type] || 'info';
      },
      getMajorLabel(value) {
        const item = this.majorOptions.find(item => item.value === value)
        return item ? item.label : value
      },
      // 获取公布类型的标签文本
      getPublishLabel(value, options) {
        if (!value) return '未设置';
        const option = options.find(opt => opt.value === value);
        return option ? option.label : value;
      },

      // 获取公布类型的标签颜色
      getPublishTagType(publishType) {
        const typeMap = {
          'after_submit': 'success',
          'after_review': 'warning',
          'after_end': 'danger',
          'never': 'info'
        };
        return typeMap[publishType] || '';
      },
      /** 获取课程单元列表 */
      getUnitList() {
        this.unitLoading = true;
        // 实际项目中应该使用API调用
        // listUnit({ courseId: this.courseId }).then(response => {
        //   this.unitList = response.rows;
        //   this.unitLoading = false;
        // });
        const courseId = this.$route.params.id
        if(courseId==='1'){
          this.unitList = this.unitList1 || null
        }
        if(courseId==='2'){
          this.unitList = this.unitList2 || null
        }
        if(courseId==='3'){
          this.unitList = this.unitList3 || null
        }
        if(courseId==='4'){
          this.unitList = this.unitList4 || null
        }
        this.unitLoading = false;

      },
      /** 编辑课程按钮 */
      handleEditCourse() {
        this.$router.push(`/education/course/edit/${this.courseId}`);
      },
      /** 添加单元按钮 */
      handleAddUnit() {
        this.resetUnitForm();
        this.unitTitle = "添加课程单元";
        this.unitOpen = true;
      },
      /** 编辑单元按钮 */
      handleEditUnit(row) {
        this.resetUnitForm();
        const id = row.id;
        // 实际项目中应该使用API调用
        // getUnit(id).then(response => {
        //   this.unitForm = response.data;
        //   this.unitOpen = true;
        //   this.unitTitle = "修改课程单元";
        // });
        this.unitForm = JSON.parse(JSON.stringify(row));
        this.unitOpen = true;
        this.unitTitle = "修改课程单元";
      },
      /** 删除单元按钮 */
      handleDeleteUnit(row) {
        const id = row.id;
        this.$confirm(`确认删除单元【${row.name}】吗？`, "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '删除中...';
              // 模拟异步操作
              setTimeout(() => {
                done();
                setTimeout(() => instance.confirmButtonLoading = false, 300);
              }, 800);
            } else {
              done();
            }
          }
        }).then(() => {
          // 实际项目中应该使用API调用
          // return deleteUnit(id).then(() => {

          // 本地删除逻辑
          const deleteIndex = this.unitList.findIndex(item => item.id === id);
          if (deleteIndex !== -1) {
            // 1. 从unitList删除
            this.unitList.splice(deleteIndex, 1);

            // 2. 同步更新关联课程数据（如果存在courseList）
            const courseIndex = this.courseList?.findIndex(c => c.id == this.courseId);
            if (courseIndex !== -1) {
              this.$set(this.courseList[courseIndex], 'unitCount', this.unitList.length);
            }

            this.$message.success(`已删除单元【${row.name}】`);
          } else {
            this.$message.warning("未找到对应单元");
          }
          // });
        }).catch(() => {
          this.$message.info("已取消删除");
        });
      },
      /** 单元状态修改 */
      handleStatusChange(row) {
        let text = row.status === "1" ? "发布" : "取消发布";
        this.$confirm('确认要"' + text + '""' + row.name + '"吗?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 实际项目中应该使用API调用
          // return updateUnit(row);
        }).then(() => {
          this.$message.success(text + "成功");
        }).catch(() => {
          row.status = row.status === "1" ? "0" : "1";
        });
      },
      /** 单元上移 */
      moveUp(index) {
        if (index > 0) {
          const temp = this.unitList[index - 1];
          this.$set(this.unitList, index - 1, this.unitList[index]);
          this.$set(this.unitList, index, temp);
          this.saveSort();
        }
      },
      /** 单元下移 */
      moveDown(index) {
        if (index < this.unitList.length - 1) {
          const temp = this.unitList[index + 1];
          this.$set(this.unitList, index + 1, this.unitList[index]);
          this.$set(this.unitList, index, temp);
          this.saveSort();
        }
      },
      /** 保存排序 */
      saveSort() {
        const ids = this.unitList.map(item => item.id);
        // 实际项目中应该使用API调用
        // sortUnit({ ids: ids.join(",") }).then(() => {
        //   this.$message.success("排序成功");
        // });
      },
      /** 添加资源按钮 */
      handleAddResource() {
        this.resetResourceForm();
        this.resourceOpen = true;
      },
      /** 从资源库选择 */
      handleSelectFromLibrary() {
        this.$message.info("从资源库选择功能待实现");
      },
      /** 添加试题按钮 */
      handleAddQuestion() {
        this.resetQuestionForm();
        this.questionOpen = true;
      },
      /** 从题库选择 */
      handleSelectQuestionFromLibrary() {
        this.$message.info("从题库选择功能待实现");
      },
      /** 删除资源 */
      handleDeleteResource(index) {
        this.unitForm.resources.splice(index, 1);
      },
      /** 删除试题 */
      handleDeleteQuestion(index) {
        this.unitForm.questions.splice(index, 1);
      },
      /** 多选框选中数据 */
      handleUnitSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      /** 获取工具名称 */
      getToolName(value) {
        const tool = this.toolOptions.find(item => item.value === value);
        return tool ? tool.label : value;
      },
      /** 题目类型变化 */
      handleQuestionTypeChange(value) {
        if (value === "single" || value === "multiple") {
          if (this.questionForm.options.length === 0) {
            this.questionForm.options = [{ content: undefined }];
          }
        } else {
          this.questionForm.options = [];
        }
        this.questionForm.answer = [];
      },
      /** 添加选项 */
      addOption() {
        this.questionForm.options.push({ content: undefined });
      },
      /** 删除选项 */
      removeOption(index) {
        this.questionForm.options.splice(index, 1);
        // 如果删除的选项是答案的一部分，需要从答案中移除
        const optionChar = String.fromCharCode(65 + index);
        const answerIndex = this.questionForm.answer.indexOf(optionChar);
        if (answerIndex !== -1) {
          this.questionForm.answer.splice(answerIndex, 1);
        }
        // 更新剩余选项的字母
        for (let i = index; i < this.questionForm.options.length; i++) {
          const oldChar = String.fromCharCode(65 + i + 1);
          const newChar = String.fromCharCode(65 + i);
          const answerIndex = this.questionForm.answer.indexOf(oldChar);
          if (answerIndex !== -1) {
            this.questionForm.answer[answerIndex] = newChar;
          }
        }
      },
      /** 提交单元表单 */
      submitUnitForm() {
        this.$refs["unitForm"].validate(valid => {
          if (valid) {
            if (this.unitForm.id != null) {
              const unitIndex = this.unitList.findIndex(u => u.id === this.unitForm.id);
              if (unitIndex !== -1) {
                const updatedUnit = {
                  ...this.unitList[unitIndex],
                  ...this.unitForm,
                  resources: this.unitForm.resources || [],
                  questions: this.unitForm.questions || [],
                  tools: this.unitForm.tools || []
                };

                this.$set(this.unitList, unitIndex, updatedUnit);
                this.$message.success("单元修改成功");
                this.unitOpen = false;
              }
            } else {
              const newId = this.unitList.length > 0
                ? Math.max(...this.unitList.map(u => u.id)) + 1
                : 1001; // 从1001开始避免与预设数据ID冲突

              const newUnit = {
                ...this.unitForm,
                id: newId,
                courseId: this.courseId,
                status: "1", // 默认状态
                createTime: new Date().toLocaleString(),
                resources: this.unitForm.resources || [],
                questions: this.unitForm.questions || [],
                tools: this.unitForm.tools || []
              };

              // 添加到列表开头并保持响应式
              this.unitList = [newUnit, ...this.unitList];
              this.$message.success("单元新增成功");
              this.unitOpen = false;
              this.resetUnitForm();
            }

          }
        });
      },
      /** 取消单元表单 */
      cancelUnitForm() {
        this.unitOpen = false;
        this.resetUnitForm();
      },
      /** 提交资源表单 */
      submitResourceForm() {
        this.$refs["resourceForm"].validate(valid => {
          if (valid) {
            this.unitForm.resources.push({ ...this.resourceForm });
            this.resourceOpen = false;
            this.resetResourceForm();
          }
        });
      },
      /** 取消资源表单 */
      cancelResourceForm() {
        this.resourceOpen = false;
        this.resetResourceForm();
      },
      /** 提交试题表单 */
      submitQuestionForm() {
        this.$refs["questionForm"].validate(valid => {
          if (valid) {
            this.unitForm.questions.push({ ...this.questionForm });
            this.questionOpen = false;
            this.resetQuestionForm();
          }
        });
      },
      /** 取消试题表单 */
      cancelQuestionForm() {
        this.questionOpen = false;
        this.resetQuestionForm();
      },
      /** 重置单元表单 */
      resetUnitForm() {
        this.unitForm = {
          id: undefined,
          name: undefined,
          type: undefined,
          startTime: undefined,
          endTime: undefined,
          duration: 60,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "0",
          tools: [],
          resources: [],
          questions: [],
          hasPython: false,
          maxSubmitTimes: 3,
          maxRunTimes: 10
        };
        this.resetForm("unitForm");
        this.activeTab = "basic";
      },
      /** 重置资源表单 */
      resetResourceForm() {
        this.resourceForm = {
          name: undefined,
          type: undefined,
          url: undefined
        };
        this.resetForm("resourceForm");
      },
      /** 重置试题表单 */
      resetQuestionForm() {
        this.questionForm = {
          type: "single",
          title: undefined,
          options: [
            { content: undefined }
          ],
          answer: [],
          analysis: undefined,
          difficulty: "1",
          score: 5
        };
        this.resetForm("questionForm");
      }
    }
  };
</script>

<style scoped>
  .el-descriptions {
    margin-top: 20px;
  }
</style>
