<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 搜索区域 -->
      <el-col :span="24">
        <el-card shadow="hover" class="search-card">
          <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="课程标题" prop="title">
              <el-input
                v-model="queryParams.title"
                placeholder="请输入课程标题"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="所属专业" prop="major">
              <el-select
                v-model="queryParams.major"
                placeholder="请选择所属专业"
                clearable
              >
                <el-option
                  v-for="item in majorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 操作按钮区域 -->
      <el-col :span="24" class="mb20">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-hasPermi="['education:course:add']"
        >新增课程</el-button>

      </el-col>

      <!-- 课程列表 -->
      <el-col :span="24">
        <el-table
          v-loading="loading"
          :data="courseList"
          row-key="id"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="课程ID" prop="id" width="80" align="center" />
          <el-table-column label="课程封面" width="120" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.thumbnail"
                fit="cover"
                :preview-src-list="[scope.row.thumbnail]"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column label="课程标题" prop="title" width="180" />
          <el-table-column label="所属专业" prop="major" width="120">
            <template slot-scope="scope">
              <span>{{ getMajorLabel(scope.row.major) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="课程简介" prop="description" show-overflow-tooltip />
          <el-table-column label="单元数量" prop="unitCount" width="100" align="center" />
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
                {{ scope.row.status === '1' ? '已发布' : '未发布' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleDetail(scope.row)"
                v-hasPermi="['education:course:query']"
              >详情</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
                v-hasPermi="['education:course:edit']"
                :disabled="scope.row.isClone"
              >编辑</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-user"
                @click="handleUser(scope.row)"
                v-hasPermi="['education:course:user']"
              >用户</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-document-copy"
                @click="handleClone(scope.row)"
                v-hasPermi="['education:course:clone']"
              >克隆</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleTransfer(scope.row)"
                v-hasPermi="['education:course:transfer']"
                :disabled="scope.row.isClone"
              >转让</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['education:course:remove']"
                :disabled="scope.row.isClone"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改课程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="课程标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入课程标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="所属专业" prop="major">
              <el-select v-model="form.major" placeholder="请选择所属专业">
                <el-option
                  v-for="item in majorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="课程简介" prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Banner图" prop="banner">
              <el-upload
                action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleBannerChange"
              >
              <el-image
                v-if="form.banner"
                :src="form.banner"
                style="width: 100%; max-height: 200px;"
                fit="contain"
              />
              <el-button v-else type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="缩略图" prop="thumbnail">
              <el-upload
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleThumbnailChange"
              >
                <el-image
                  v-if="form.thumbnail"
                  :src="form.thumbnail"
                  style="width: 150px; height: 150px;"
                  fit="cover"
                />
                <el-button v-else type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  // import { listCourse, getCourse, delCourse, addCourse, updateCourse, exportCourse, cloneCourse } from "@/api/education/course";
  import ImageUpload from '@/components/ImageUpload';

  export default {
    name: "Course",
    components: { ImageUpload },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 课程表格数据
        courseList: [
          {
            id: 1,
            title: "Python编程基础",
            description: "本课程将带领学员从零开始学习Python编程语言，掌握基础语法和常用数据结构。",
            major: "computer",
            banner: require("@/assets/ys/python编程基础banner.jpg"),
            thumbnail: require("@/assets/ys/python编程基础.jpg"),
            unitCount: 12,
            status: "1",
            createTime: "2023-05-10 14:30:22",
            isClone: false
          },
          {
            id: 2,
            title: "机器学习入门",
            description: "介绍机器学习的基本概念和常用算法，包括监督学习和无监督学习。",
            major: "ai",
            banner: require("@/assets/ys/机器学习banner.jpg"),
            thumbnail: require("@/assets/ys/机器学习入门.jpg"),
            unitCount: 8,
            status: "1",
            createTime: "2023-06-15 09:12:45",
            isClone: false
          },
          {
            id: 3,
            title: "Web前端开发",
            description: "学习HTML、CSS和JavaScript等前端开发技术，构建响应式网站。",
            major: "computer",
            banner: require("@/assets/ys/web前端开发benner.jpg"),
            thumbnail: require("@/assets/ys/web前端.jpg"),
            unitCount: 10,
            status: "0",
            createTime: "2023-07-20 16:45:33",
            isClone: true
          },
          {
            id: 4,
            title: "数据分析实战",
            description: "使用Python进行数据清洗、分析和可视化，掌握Pandas和Matplotlib等工具。",
            major: "data",
            banner: require("@/assets/ys/数据分析实战banner.jpg"),
            thumbnail: require("@/assets/ys/数据分析实战.jpg"),
            unitCount: 6,
            status: "1",
            createTime: "2023-08-05 11:20:18",
            isClone: false
          }
        ],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 状态数据字典
        statusOptions: [
          { value: "0", label: "未发布" },
          { value: "1", label: "已发布" }
        ],
        // 专业数据字典
        majorOptions: [
          { value: "computer", label: "计算机" },
          { value: "ai", label: "人工智能" },
          { value: "data", label: "数据科学" },
          { value: "math", label: "数学" },
          { value: "physics", label: "物理" }
        ],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          title: undefined,
          major: undefined,
          status: undefined
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          title: [
            { required: true, message: "课程标题不能为空", trigger: "blur" }
          ],
          major: [
            { required: true, message: "所属专业不能为空", trigger: "change" }
          ],
          status: [
            { required: true, message: "状态不能为空", trigger: "change" }
          ]
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询课程列表 */
      getList() {
        this.loading = false;
        // 实际项目中应该使用API调用
        // this.loading = true;
        // listCourse(this.queryParams).then(response => {
        //   this.courseList = response.rows;
        //   this.total = response.total;
        //   this.loading = false;
        // });
      },
      resetForm() {
        this.form = {
          id: null,
          title: "",
          description: "",
          major: "",
          banner: "",
          thumbnail: "",
          unitCount: 0,
          status: "1"
        };
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      },
      handleBannerChange(file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          this.form.banner = e.target.result
        }
        reader.readAsDataURL(file.raw)
      },

      // 处理缩略图变化
      handleThumbnailChange(file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          this.form.thumbnail = e.target.result
        }
        reader.readAsDataURL(file.raw)
      },

      getMajorLabel(value) {
        const item = this.majorOptions.find(item => item.value === value)
        return item ? item.label : value
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          title: undefined,
          description: undefined,
          major: undefined,
          banner: undefined,
          thumbnail: undefined,
          status: "0"
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加课程";
      },
      /** 详情按钮操作 */
      handleDetail(row) {
        this.$router.push(`/kechengguanli/detail/${row.id}`);
      },
      /** 编辑按钮操作 */
      handleEdit(row) {
        this.reset();
        const id = row.id || this.ids;
        // 实际项目中应该使用API调用
        // getCourse(id).then(response => {
        //   this.form = response.data;
        //   this.open = true;
        //   this.title = "修改课程";
        // });
        this.form = JSON.parse(JSON.stringify(row));
        this.open = true;
        this.title = "修改课程";
      },
      /** 用户管理按钮操作 */
      handleUser(row) {
        this.$router.push(`/kechengguanli/users/${row.id}`);
      },
      /** 克隆按钮操作 */
      handleClone(row) {
        this.$confirm('确认要克隆课程"' + row.title + '"吗?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 实际项目中应该使用API调用
          // return cloneCourse(row.id);
        }).then(() => {
          this.getList();
          this.$message.success("克隆成功");
        }).catch(() => {});
      },
      /** 转让按钮操作 */
      handleTransfer(row) {
        this.$prompt('请输入接收用户的用户名', '转让课程', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^[a-zA-Z0-9_]{4,20}$/,
          inputErrorMessage: '用户名格式不正确'
        }).then(({ value }) => {
          this.$message.success(`课程已转让给用户: ${value}`);
          this.getList();
        }).catch(() => {});
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              // 修改逻辑
              this.$message.success("修改成功");
              // 查找并更新列表中对应的课程
              const index = this.courseList.findIndex(item => item.id === this.form.id);
              if (index !== -1) {
                // 保留原数据的引用关系（避免Vue响应性问题）
                const updatedCourse = {
                  ...this.courseList[index],
                  ...this.form,
                  // 特殊处理图片引用（如果是本地预览的Base64）
                  banner: this.form.banner || this.courseList[index].banner,
                  thumbnail: this.form.thumbnail || this.courseList[index].thumbnail
                };
                this.$set(this.courseList, index, updatedCourse);
              }
              this.open = false;
            } else {
              // 新增逻辑
              this.$message.success("新增成功");
              // 生成模拟ID（实际项目中应由后端生成）
              const newId = this.courseList.length > 0
                ? Math.max(...this.courseList.map(item => item.id)) + 1
                : 1;
              // 添加新课程到列表
              this.courseList.unshift({
                ...this.form,
                id: newId,
                createTime: new Date().toLocaleString(),
                status: "1", // 默认启用状态
                unitCount: 0  // 默认单元数
              });
              this.open = false;
            }
            // 重置表单
            this.resetForm();
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids;
        const idArray = Array.isArray(ids) ? ids : [ids]; // 统一转为数组处理

        this.$confirm(
          `确认删除${idArray.length > 1 ? '选中的' : ''}课程${idArray.length > 1 ? '（共'+idArray.length+'项）' : '编号为"' + ids + '"'}?`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true;
                instance.confirmButtonText = '删除中...';
                // 模拟异步操作
                setTimeout(() => {
                  done();
                  setTimeout(() => {
                    instance.confirmButtonLoading = false;
                  }, 300);
                }, 800);
              } else {
                done();
              }
            }
          }
        ).then(() => {
          // 实际项目中应该使用API调用
          // return delCourse(ids).then(() => {
          // 本地删除逻辑
          idArray.forEach(id => {
            const index = this.courseList.findIndex(item => item.id === id);
            if (index !== -1) {
              this.courseList.splice(index, 1);
            }
          });

          // 清除已选中的ID（如果有多选）
          this.ids = [];
          this.$message.success(`成功删除 ${idArray.length} 条数据`);
          // });
        }).catch(() => {
          this.$message.info('已取消删除');
        });
      },
      /** 导出按钮操作 */
      handleExport() {
        const queryParams = this.queryParams;
        this.$confirm('是否确认导出所有课程数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 实际项目中应该使用API调用
          // return exportCourse(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(() => {});
      }
    }
  };
</script>

<style scoped>
  .search-card {
    margin-bottom: 20px; /* 增加底部间距 */
  }
  .course-cover {
    width: 100px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
  }
</style>
