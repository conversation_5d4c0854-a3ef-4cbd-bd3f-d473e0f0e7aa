<template>
  <div class="app-container">
    <el-row :gutter="50" style="display: flex;border-bottom: 1px solid #eee;padding:0 20px 20px 20px;">
      <el-col :span="4">
        <div class="imgBox">
          <img class="devImg" fit="fill" :src="courseInfo.thumbnail" />
        </div>
      </el-col>
      <el-col :span="18">
        <el-descriptions :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'>
          <el-descriptions-item label="课程代码"> {{courseInfo.courseCode}}</el-descriptions-item>
          <el-descriptions-item label="课程名称"> {{courseInfo.courseName}}</el-descriptions-item>
          <el-descriptions-item label="课程所属人"> {{courseInfo.createBy}}</el-descriptions-item>
          <el-descriptions-item label="课程简介" :span="3"> {{courseInfo.description}}</el-descriptions-item>
          <el-descriptions-item label="更新时间"> {{courseInfo.lastActiveTime}}</el-descriptions-item>
        </el-descriptions>
      </el-col>
    </el-row>
    <el-table :data="unitList" style="width: 100%">
      <el-table-column prop="name" label="课程单元名称" width="180" />
      <el-table-column prop="startTime" label="发布日期" width="180" />
      <el-table-column prop="userNumber" label="课堂人数" />
      <el-table-column prop="endTime" label="课堂开始截止时间" />
      <el-table-column prop="tools" label="课程单元启用的工具">
        <template slot-scope="scope">
          <span>{{changeTools(scope.row.tools)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="教学素材个数">
        <template slot-scope="scope">
          <span>{{scope.row.resources.length}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="题目个数">
        <template slot-scope="scope">
          <span>{{scope.row.questions.length}}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: 'courseInfos',
  props: {
  },
  data() {
    return {
      courseInfo: {},
      contentStyle: {
        fontSize: '14px'
      },
      labelStyle: {
        fontWeight: 'bold',
        fontSize: '14px'
      },
      unitList1: [{
        id: 102,
        name: "Python基础语法",
        type: "text",
        startTime: "2023-09-08 08:00:00",
        endTime: "2023-09-14 23:59:59",
        userNumber: 5,
        duration: 120,
        answerPublish: "after_review",
        analysisPublish: "after_review",
        scorePublish: "after_review",
        status: "1",
        tools: ["ai_assistant", "ai_lab"],
        resources: [
          { id: 1003, name: "Python语法速查表", type: "doc", url: "https://example.com/resources/cheatsheet.pdf" }
        ],
        questions: [
          { id: 2003, title: "Python变量命名规则", type: "single", difficulty: "1", score: 5 },
          { id: 2004, title: "Python数据类型有哪些？", type: "multiple", difficulty: "2", score: 10 },
          { id: 2005, title: "编写一个计算圆面积的函数", type: "program", difficulty: "3", score: 20 }
        ],
        hasPython: true,
        maxSubmitTimes: 5,
        maxRunTimes: 20
      },
      {
        id: 103,
        name: "Python流程控制",
        type: "practice",
        startTime: "2023-09-15 08:00:00",
        endTime: "2023-09-21 23:59:59",
        userNumber: 5,
        duration: 180,
        answerPublish: "after_end",
        analysisPublish: "after_end",
        scorePublish: "after_end",
        status: "0",
        tools: ["ai_assistant", "virtual_machine"],
        resources: [
          { id: 1004, name: "流程控制练习题", type: "doc", url: "https://example.com/resources/control.pdf" },
          { id: 1005, name: "示例代码", type: "code", url: "https://example.com/resources/examples.zip" }
        ],
        questions: [
          { id: 2006, title: "if-else语句的使用场景", type: "short", difficulty: "2", score: 10 },
          { id: 2007, title: "编写一个猜数字游戏", type: "program", difficulty: "4", score: 30 }
        ],
        hasPython: true,
        maxSubmitTimes: 3,
        maxRunTimes: 15
      }
      ],
      unitList2: [
        {
          id: 201,
          name: "机器学习概述",
          type: "video",
          startTime: "2023-10-01 08:00:00",
          endTime: "2023-10-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2001, name: "机器学习发展史", type: "doc", url: "https://example.com/ml/history.pdf" },
            { id: 2002, name: "基础概念讲解视频", type: "video", url: "https://example.com/ml/concepts.mp4" }
          ],
          questions: [
            { id: 3001, title: "什么是监督学习？", type: "single", difficulty: "1", score: 5 },
            { id: 3002, title: "机器学习的三大类型", type: "multiple", difficulty: "2", score: 10 }
          ],
          hasPython: false
        },
        {
          id: 202,
          name: "线性回归实践",
          type: "practice",
          startTime: "2023-10-08 08:00:00",
          endTime: "2023-10-14 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine", "toolkit"],
          resources: [
            { id: 2003, name: "Scikit-learn使用指南", type: "doc", url: "https://example.com/ml/sklearn.pdf" },
            { id: 2004, name: "房价数据集", type: "dataset", url: "https://example.com/ml/housing.csv" }
          ],
          questions: [
            { id: 3003, title: "线性回归的损失函数", type: "short", difficulty: "3", score: 15 },
            { id: 3004, title: "实现波士顿房价预测", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 25
        },
        {
          id: 203,
          name: "分类算法",
          type: "text",
          startTime: "2023-10-15 08:00:00",
          endTime: "2023-10-21 23:59:59",
          userNumber: 5,
          duration: 150,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2005, name: "逻辑回归原理", type: "doc", url: "https://example.com/ml/logistic.pdf" },
            { id: 2006, name: "鸢尾花数据集", type: "dataset", url: "https://example.com/ml/iris.csv" }
          ],
          questions: [
            { id: 3005, title: "SVM的核心思想", type: "short", difficulty: "3", score: 15 },
            { id: 3006, title: "实现手写数字识别", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 20
        }
      ],

      unitList3: [
        {
          id: 301,
          name: "HTML5基础",
          type: "video",
          startTime: "2023-11-01 08:00:00",
          endTime: "2023-11-07 23:59:59",
          userNumber: 5,
          duration: 80,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "virtual_machine"],
          resources: [
            { id: 3001, name: "HTML5标签手册", type: "doc", url: "https://example.com/web/html5.pdf" },
            { id: 3002, name: "基础布局教学", type: "video", url: "https://example.com/web/layout.mp4" }
          ],
          questions: [
            { id: 4001, title: "HTML5新特性", type: "multiple", difficulty: "2", score: 10 },
            { id: 4002, title: "构建个人简介页面", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: false
        },
        {
          id: 302,
          name: "CSS3样式设计",
          type: "practice",
          startTime: "2023-11-08 08:00:00",
          endTime: "2023-11-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "pose_editor"],
          resources: [
            { id: 3003, name: "CSS3动画指南", type: "doc", url: "https://example.com/web/css3.pdf" },
            { id: 3004, name: "Flex布局示例", type: "code", url: "https://example.com/web/flex.zip" }
          ],
          questions: [
            { id: 4003, title: "BFC是什么？", type: "short", difficulty: "3", score: 15 },
            { id: 4004, title: "实现响应式导航栏", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: false
        },
        {
          id: 303,
          name: "JavaScript交互",
          type: "text",
          startTime: "2023-11-15 08:00:00",
          endTime: "2023-11-21 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["virtual_machine", "toolkit"],
          resources: [
            { id: 3005, name: "ES6特性速查", type: "doc", url: "https://example.com/web/es6.pdf" },
            { id: 3006, name: "DOM操作示例", type: "code", url: "https://example.com/web/dom.zip" }
          ],
          questions: [
            { id: 4005, title: "闭包的概念", type: "short", difficulty: "4", score: 20 },
            { id: 4006, title: "实现购物车功能", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: false
        }
      ],

      unitList4: [
        {
          id: 401,
          name: "Pandas基础",
          type: "video",
          startTime: "2023-12-01 08:00:00",
          endTime: "2023-12-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine"],
          resources: [
            { id: 4001, name: "Pandas速查表", type: "doc", url: "https://example.com/da/pandas.pdf" },
            { id: 4002, name: "数据清洗演示", type: "video", url: "https://example.com/da/cleaning.mp4" }
          ],
          questions: [
            { id: 5001, title: "Series和DataFrame区别", type: "short", difficulty: "2", score: 10 },
            { id: 5002, title: "数据去重操作", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 20
        },
        {
          id: 402,
          name: "数据可视化",
          type: "practice",
          startTime: "2023-12-08 08:00:00",
          endTime: "2023-12-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_image"],
          resources: [
            { id: 4003, name: "Matplotlib指南", type: "doc", url: "https://example.com/da/matplotlib.pdf" },
            { id: 4004, name: "销售数据集", type: "dataset", url: "https://example.com/da/sales.csv" }
          ],
          questions: [
            { id: 5003, title: "折线图和柱状图适用场景", type: "short", difficulty: "3", score: 15 },
            { id: 5004, title: "绘制销售趋势图", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 15
        },
        {
          id: 403,
          name: "实战项目",
          type: "project",
          startTime: "2023-12-15 08:00:00",
          endTime: "2023-12-21 23:59:59",
          userNumber: 5,
          duration: 240,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "toolkit", "material_lib"],
          resources: [
            { id: 4005, name: "电商用户行为数据", type: "dataset", url: "https://example.com/da/ecommerce.csv" },
            { id: 4006, name: "分析报告模板", type: "doc", url: "https://example.com/da/template.pdf" }
          ],
          questions: [
            { id: 5005, title: "用户购买行为分析", type: "program", difficulty: "5", score: 50 }
          ],
          hasPython: true,
          maxSubmitTimes: 3,
          maxRunTimes: 10
        }
      ],
      unitList: [],
      toolOptions: [
        { value: "ai_assistant", label: "AI助手" },
        { value: "ai_image", label: "AI生图" },
        { value: "ai_lab", label: "AI对话实验室" },
        { value: "virtual_machine", label: "虚拟机" },
        { value: "toolkit", label: "工具包" },
        { value: "material_lib", label: "素材库" },
        { value: "pose_editor", label: "姿态编辑器" }
      ],
    }
  },
  created() {
    this.courseInfo = this.$router.history.current.params.data
    this.getUnitList()
  },
  activated() {
    this.courseInfo = this.$router.history.current.params.data
  },
  methods: {
    getUnitList() {
      this.unitList = this.courseInfo.courseCode == 1000001 ? this.unitList1 : this.courseInfo.courseCode == 1000002 ? this.unitList2 : this.courseInfo.courseCode == 1000003 ? this.unitList3 : this.unitList4
    },
    changeTools(val) {
      const toolsName = []
      val.map(item => {
        const obj = this.toolOptions.find(i => i.value == item)
        if (obj) {
          toolsName.push(obj.label)
        }
      })
      return toolsName.join('，')
    }
  }
}
</script>
<style lang="scss" scoped >
.imgBox {
  width: 100%;
  height: 100%;
  position: relative;
  .devImg {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
    max-height: 100%;
    border: 1px solid #ccc;
  }
}
</style>
