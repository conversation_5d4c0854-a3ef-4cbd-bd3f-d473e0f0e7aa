<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="课程简介" prop="description">
        <el-input v-model="queryParams.description" placeholder="请输入课程简介" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">加入课程</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="courseList">
      <el-table-column label="课程代码" prop="courseCode" min-width="100" />
      <el-table-column label="课程头像" prop="thumbnail" min-width="100">
        <template slot-scope="scope">
          <img :src="scope.row.thumbnail" alt="图片" style="width: 100px; height: 100px;" />
        </template>
      </el-table-column>
      <el-table-column label="课程名称" prop="courseName" min-width="100">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.courseName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程创建人" prop="createBy" min-width="100" />
      <el-table-column label="加入状态" prop="joinStatus" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.joinStatus === '1'">已加入</span>
          <span v-else>未加入</span>
        </template>
      </el-table-column>
      <el-table-column label="加入课程人数" prop="peopleNum" min-width="100" />
      <el-table-column label="最后活跃时间" prop="lastActiveTime" min-width="100" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" v-if="scope.row.joinStatus == '0'"
            @click="handleJoin(scope.row)">加入课程</el-button>
          <el-button size="mini" type="text" v-if="scope.row.joinStatus == '1'"
            @click="handleGoClass(scope.row)">去上课</el-button>
          <el-button size="mini" type="text" v-if="scope.row.joinStatus == '1'"
            @click="handleDrop(scope.row)">退出课程</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="500px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="100px">
        <el-form-item label="课程代码" prop="courseCode">
          <el-input v-model="form.courseCode" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">加 入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "TeachingManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      courseList: [],
      allCourseList: [
        {
          courseCode: 1000001,
          courseName: "Python编程基础",
          description: "本课程将带领学员从零开始学习Python编程语言，掌握基础语法和常用数据结构。",
          thumbnail: require("@/assets/ys/python编程基础.jpg"),
          createBy: '张三',
          joinStatus: '0',
          peopleNum: '22',
          lastActiveTime: '2023-08-05 11:20:18'
        },
        {
          courseCode: 1000002,
          courseName: "机器学习入门",
          description: "介绍机器学习的基本概念和常用算法，包括监督学习和无监督学习。",
          thumbnail: require("@/assets/ys/机器学习入门.jpg"),
          createBy: '张三',
          joinStatus: '0',
          peopleNum: '22',
          lastActiveTime: '2023-08-05 11:20:18'
        },
        {
          courseCode: 1000003,
          courseName: "Web前端开发",
          description: "学习HTML、CSS和JavaScript等前端开发技术，构建响应式网站。",
          thumbnail: require("@/assets/ys/web前端.jpg"),
          createBy: '张三',
          joinStatus: '0',
          peopleNum: '22',
          lastActiveTime: '2023-08-05 11:20:18'
        },
        {
          courseCode: 1000004,
          courseName: "数据分析实战",
          description: "使用Python进行数据清洗、分析和可视化，掌握Pandas和Matplotlib等工具。",
          thumbnail: require("@/assets/ys/数据分析实战.jpg"),
          createBy: '张三',
          joinStatus: '0',
          peopleNum: '22',
          lastActiveTime: '2023-08-05 11:20:18'
        },
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      title: '加入课程',
      form: {},
      rules: {
        courseCode: [
          { required: true, message: '请输入课程代码', trigger: ['blur', 'change'] },
        ],
      },
      dialogOpen: false,
      btnLoading: false,
      dialogVisible: false,
      isStudent: false,
    };
  },
  created() {
    this.getList();
    this.getUserRole();
  },
  activated() {
    this.getList();
    this.getUserRole();
  },
  methods: {

    getUserRole() {
      const userRoles = this.$store.state.user.roles;
      if (userRoles && userRoles.includes('teacher')) {
        this.isStudent = false
      } else if (userRoles && userRoles.includes('student')) {
        this.isStudent = true;
      }
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      const startIndex = (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      const endIndex = startIndex + this.queryParams.pageSize
      this.courseList = this.allCourseList.slice(startIndex, endIndex)
      this.total = this.allCourseList.length
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.loading = true;
      this.queryParams.pageNum = 1;
      const queryCourseList = this.allCourseList.filter(item => {
        return (
          (!this.queryParams.courseName || this.queryParams.courseName == '' || item.courseName.toLowerCase().includes(this.queryParams.courseName.toLowerCase())) &&
          (!this.queryParams.description || this.queryParams.description == '' || item.description.toLowerCase().includes(this.queryParams.description.toLowerCase()))
        );
      });
      const startIndex = (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      const endIndex = startIndex + this.queryParams.pageSize
      this.courseList = queryCourseList.slice(startIndex, endIndex)
      this.total = queryCourseList.length
      this.loading = false;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 加入课程按钮操作 */
    handleAdd() {
      this.dialogOpen = true;
      this.title = '加入课程';
    },
    handleJoin(items) {
      let index = this.allCourseList.findIndex(item => item.courseCode == items.courseCode)
      this.allCourseList[index].joinStatus = '1'
      this.$message.success('已加入此课程')
      this.getList()
    },
    handleDrop(items) {
      let index = this.allCourseList.findIndex(item => item.courseCode == items.courseCode)
      this.allCourseList[index].joinStatus = '0'
      this.$message.success('已退出入此课程')
      this.getList()
    },
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm()) {
        let index = this.allCourseList.findIndex(item => item.courseCode == this.form.courseCode)
        if (index !== -1) {
          this.allCourseList[index].joinStatus = '1'
          this.$message.success('已加入此课程')
          this.btnLoading = false
          this.handleClose()
        } else {
          this.$message.error('无此课程代码')
          this.btnLoading = false
        }
      } else {
        this.btnLoading = false
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      this.resetForm("form");
      this.dialogOpen = false
      this.getList()
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleReview(row) {
      this.$router.push({
        name: "CourseInfos",
        params: {
          data: row
        }
      });
    },
    handleGoClass(row) {
      if (this.isStudent) {
        this.$router.push({
          name: "ClassPage",
          params: {
            data: row
          }
        });
      } else {
        this.$router.push({
          name: "TeachingPage",
          params: {
            data: row
          }
        });
      }

    }

  },
};
</script>
<style lang="scss">
.ck-input {
  width: 300px;
}
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>



