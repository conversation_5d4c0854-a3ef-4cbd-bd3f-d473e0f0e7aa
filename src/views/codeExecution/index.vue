<template>
  <div class="code-execution-container" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧代码编辑器 -->
      <div class="editor-panel" :style="editorPanelStyle" :class="{ 'fullscreen': isFullscreen }">
        <div class="panel-header">
          <div class="header-left">
            <div class="header-icon">
              <i class="el-icon-edit-outline"></i>
            </div>
            <div class="header-title">
              <span class="title">代码编辑器</span>
              <span class="subtitle">{{ selectedLanguage.toUpperCase() }}</span>
            </div>
          </div>
          <div class="editor-controls">
            <div class="control-group">
              <el-select
                v-model="selectedLanguage"
                size="small"
                class="language-select"
                popper-class="custom-select-dropdown"
                @change="handleLanguageChange"
              >
                <el-option
                  v-for="lang in supportedLanguages"
                  :key="lang.value"
                  :label="lang.label"
                  :value="lang.value"
                >
                  <div class="option-content">
                    <i :class="lang.icon"></i>
                    <span class="option-text">{{ lang.label }}</span>
                  </div>
                </el-option>
              </el-select>

              <el-button
                size="small"
                class="theme-btn glass-btn"
                @click="changeTheme"
                :title="'当前主题: ' + currentTheme.name"
              >
                <i :class="currentTheme.icon"></i>
                <span>{{ currentTheme.name }}</span>
              </el-button>

              <el-button
                size="small"
                class="fullscreen-btn glass-btn"
                @click="toggleFullscreen"
                :title="isFullscreen ? '退出全屏' : '全屏编辑'"
              >
                <i :class="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"></i>
                <span>{{ isFullscreen ? '退出全屏' : '全屏' }}</span>
              </el-button>

              <el-button
                v-if="!isFullscreen"
                size="small"
                class="expand-btn glass-btn"
                @click="toggleEditorSize"
                :title="isEditorExpanded ? '收缩编辑器' : '展开编辑器'"
              >
                <i :class="isEditorExpanded ? 'el-icon-s-fold' : 'el-icon-s-unfold'"></i>
                <span>{{ isEditorExpanded ? '收缩' : '展开' }}</span>
              </el-button>
            </div>
          </div>
        </div>
        <div class="editor-container" id="editorContainer" ref="editorContainer">
          <div class="editor-loading" v-if="!editor">
            <div class="loading-spinner"></div>
            <span>编辑器加载中...</span>
          </div>
        </div>
      </div>

      <!-- 分割线 -->
      <div
        v-if="!isFullscreen"
        class="resize-handle"
        @mousedown="startResize"
        :class="{ 'resizing': isResizing }"
      >
        <div class="resize-indicator">
          <div class="resize-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <!-- 右侧结果展示 -->
      <div
        v-if="!isFullscreen"
        class="result-panel"
        :style="resultPanelStyle"
      >
        <div class="panel-header">
          <div class="header-left">
            <div class="header-icon result-icon">
              <i class="el-icon-monitor"></i>
            </div>
            <div class="header-title">
              <span class="title">结果</span>
              <span class="subtitle" v-if="executionResult">
                {{ getStatusText(executionResult.status) }}
              </span>
            </div>
          </div>

          <div class="result-controls">
            <div class="control-group">
              <el-button
                type="primary"
                size="small"
                class="run-btn primary-btn"
                :loading="isExecuting"
                @click="executeCode"
                :disabled="!code.trim()"
              >
                <i class="el-icon-video-play"></i>
                <span>{{ isExecuting ? '执行中...' : '运行代码' }}</span>
              </el-button>

              <el-dropdown trigger="click" class="more-actions">
                <el-button size="small" class="action-btn glass-btn">
                  <i class="el-icon-more"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" class="custom-dropdown">
                  <el-dropdown-item @click.native="clearCode">
                    <i class="el-icon-delete"></i> 清空代码
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="resetCode">
                    <i class="el-icon-refresh"></i> 重置代码
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="clearResult">
                    <i class="el-icon-circle-close"></i> 清空结果
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 执行状态 -->
        <div class="execution-status" v-if="executionResult">
          <div class="header-container-id" v-if="executionResult">
            容器id:{{getShortContainerId(executionResult.containerId)}}
          </div>

          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon" :class="executionResult.status === 'SUCCESS' ? 'success' : 'error'">
                <i :class="executionResult.status === 'SUCCESS' ? 'el-icon-success' : 'el-icon-error'"></i>
              </div>
              <div class="status-info">
                <span class="status-label">状态</span>
                <span class="status-value">{{ getStatusText(executionResult.status) }}</span>
              </div>
            </div>

            <div class="status-card" v-if="executionResult.executionTime">
              <div class="status-icon time">
                <i class="el-icon-time"></i>
              </div>
              <div class="status-info">
                <span class="status-label">执行时间</span>
                <span class="status-value">{{ executionResult.executionTime }}ms</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输出结果 -->
        <div class="output-container">
          <!-- 标准输出 -->
          <div class="output-section" v-if="executionResult && executionResult.stdout">
            <div class="output-header success">
              <div class="output-title">
                <i class="el-icon-success"></i>
                <span>标准输出</span>
              </div>
              <el-button size="mini" type="text" @click="copyOutput(executionResult.stdout)" class="copy-btn">
                <i class="el-icon-copy-document"></i>
                <span>复制</span>
              </el-button>
            </div>
            <div class="output-content-wrapper">
              <pre class="output-content success">{{ formatOutput(executionResult.stdout) }}</pre>
            </div>
          </div>

          <!-- 错误输出 -->
          <div class="output-section" v-if="executionResult && executionResult.stderr">
            <div class="output-header error">
              <div class="output-title">
                <i class="el-icon-error"></i>
                <span>错误输出</span>
              </div>
              <el-button size="mini" type="text" @click="copyOutput(executionResult.stderr)" class="copy-btn">
                <i class="el-icon-copy-document"></i>
                <span>复制</span>
              </el-button>
            </div>
            <div class="output-content-wrapper">
              <pre class="output-content error">{{ executionResult.stderr }}</pre>
            </div>
          </div>

          <!-- 错误信息 -->
          <div class="output-section" v-if="executionResult && executionResult.errorMessage">
            <div class="output-header error">
              <div class="output-title">
                <i class="el-icon-warning"></i>
                <span>错误信息</span>
              </div>
            </div>
            <div class="output-content-wrapper">
              <pre class="output-content error">{{ executionResult.errorMessage }}</pre>
            </div>
          </div>

          <!-- 空状态 -->
          <div class="empty-result" v-if="!executionResult && !isExecuting">
            <div class="empty-illustration">
              <div class="empty-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="empty-waves">
                <div class="wave"></div>
                <div class="wave"></div>
                <div class="wave"></div>
              </div>
            </div>
            <div class="empty-content">
              <h3 class="empty-title">准备就绪</h3>
              <p class="empty-text">点击"运行代码"按钮执行您的代码</p>
              <p class="empty-hint">支持 Python、JavaScript、Java 等多种语言</p>
            </div>
          </div>

          <!-- 执行中状态 -->
          <div class="executing-state" v-if="isExecuting">
            <div class="executing-animation">
              <div class="code-lines">
                <div class="code-line" v-for="i in 5" :key="i" :style="{ animationDelay: i * 0.1 + 's' }"></div>
              </div>
              <div class="executing-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
              </div>
            </div>
            <div class="executing-content">
              <h3 class="executing-title">代码执行中</h3>
              <p class="executing-text">请稍候，正在处理您的代码...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏模式下的浮动控制面板 -->
    <div v-if="isFullscreen" class="fullscreen-controls">
      <div class="floating-panel">
        <el-button
          type="primary"
          size="small"
          class="run-btn primary-btn"
          :loading="isExecuting"
          @click="executeCode"
          :disabled="!code.trim()"
        >
          <i class="el-icon-video-play"></i>
          <span>{{ isExecuting ? '执行中...' : '运行' }}</span>
        </el-button>

        <el-button size="small" @click="toggleFullscreen" class="exit-fullscreen-btn glass-btn">
          <i class="el-icon-copy-document"></i>
          <span>退出全屏</span>
        </el-button>
      </div>

      <!-- 全屏模式下的执行结果弹窗 -->
      <el-dialog
        title="结果"
        :visible.sync="showResultDialog"
        width="60%"
        :modal="false"
        :close-on-click-modal="false"
        class="result-dialog"
      >
        <div class="dialog-content">
          <!-- 执行状态 -->
          <div class="execution-status" v-if="executionResult">
            <el-tag
              :type="executionResult.status === 'SUCCESS' ? 'success' : 'danger'"
              size="small"
            >
              {{ getStatusText(executionResult.status) }}
            </el-tag>
            <span v-if="executionResult.executionTime" class="execution-time">
              执行时间: {{ executionResult.executionTime }}ms
            </span>
          </div>

          <!-- 输出内容 -->
          <div v-if="executionResult && executionResult.stdout" class="dialog-output">
            <h4>标准输出:</h4>
            <pre>{{ formatOutput(executionResult.stdout) }}</pre>
          </div>

          <div v-if="executionResult && executionResult.stderr" class="dialog-output error">
            <h4>错误输出:</h4>
            <pre>{{ executionResult.stderr }}</pre>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {executeCode} from '@/api/codeExecution'
import * as monaco from 'monaco-editor';
export default {
  name: 'CodeExecution',
  data() {
    return {
      // 编辑器相关
      editor: null,
      editorConfig:{},
      // 支持的编程语言配置
      supportedLanguages: [
        {
          label: 'Python',
          value: 'python',
          icon: 'fab fa-python',
          monacoLang: 'python'
        },
        {
          label: 'JavaScript',
          value: 'javascript',
          icon: 'fab fa-js-square',
          monacoLang: 'javascript'
        },
        {
          label: 'Java',
          value: 'java',
          icon: 'fab fa-java',
          monacoLang: 'java'
        },
        {
          label: 'C',
          value: 'c',
          icon: 'fas fa-code',
          monacoLang: 'c'
        },
        {
          label: 'C++',
          value: 'cpp',
          icon: 'fas fa-code',
          monacoLang: 'cpp'
        }
      ],
      // 各语言的默认代码模板
      languageTemplates: {
        python: `# 🚀 欢迎使用在线代码执行平台
# 这里是一个精美的Python示例

import math
import time

def welcome_message():
    """显示欢迎信息"""
    print("🎉 欢迎使用代码执行平台!")

def calculate_circle_area(radius):
    """计算圆的面积"""
    area = math.pi * radius ** 2
    return area

def main():
    # 显示欢迎信息
    welcome_message()

    # 计算圆的面积
    radius = 5
    area = calculate_circle_area(radius)
    print(f"📐 半径为 {radius} 的圆的面积是: {area:.2f}")

    print("✨ 代码执行完成!")

if __name__ == "__main__":
    main()`,

        javascript: `// 🚀 欢迎使用在线代码执行平台
// 这里是一个精美的JavaScript示例

function welcomeMessage() {
    console.log("🎉 欢迎使用代码执行平台!");
}

function calculateCircleArea(radius) {
    const area = Math.PI * radius ** 2;
    return area;
}

function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

function main() {
    // 显示欢迎信息
    welcomeMessage();

    // 计算圆的面积
    const radius = 5;
    const area = calculateCircleArea(radius);
    console.log(\`📐 半径为 \${radius} 的圆的面积是: \${area.toFixed(2)}\`);

    // 计算斐波那契数列
    console.log("\\n🔢 斐波那契数列前10项:");
    for (let i = 0; i < 10; i++) {
        const fibValue = fibonacci(i);
        console.log(\`F(\${i}) = \${fibValue}\`);
    }

    console.log("\\n✨ 代码执行完成!");
}

main();`,

        java: `// 🚀 欢迎使用在线代码执行平台
// 这里是一个精美的Java示例

public class Main {

    public static void welcomeMessage() {
        System.out.println("🎉 欢迎使用代码执行平台!");
    }

    public static double calculateCircleArea(double radius) {
        return Math.PI * radius * radius;
    }

    public static int fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }

    public static void main(String[] args) {
        // 显示欢迎信息
        welcomeMessage();

        // 计算圆的面积
        double radius = 5.0;
        double area = calculateCircleArea(radius);
        System.out.printf("📐 半径为 %.1f 的圆的面积是: %.2f%n", radius, area);

        // 计算斐波那契数列
        System.out.println("\\n🔢 斐波那契数列前10项:");
        for (int i = 0; i < 10; i++) {
            int fibValue = fibonacci(i);
            System.out.printf("F(%d) = %d%n", i, fibValue);
        }

        System.out.println("\\n✨ 代码执行完成!");
    }
}`,

        c: `// 🚀 欢迎使用在线代码执行平台
// 这里是一个精美的C语言示例

#include <stdio.h>
#include <math.h>

void welcome_message() {
    printf("🎉 欢迎使用代码执行平台!\\n");
}

double calculate_circle_area(double radius) {
    return M_PI * radius * radius;
}

int fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

int main() {
    // 显示欢迎信息
    welcome_message();

    // 计算圆的面积
    double radius = 5.0;
    double area = calculate_circle_area(radius);
    printf("📐 半径为 %.1f 的圆的面积是: %.2f\\n", radius, area);

    // 计算斐波那契数列
    printf("\\n🔢 斐波那契数列前10项:\\n");
    for (int i = 0; i < 10; i++) {
        int fib_value = fibonacci(i);
        printf("F(%d) = %d\\n", i, fib_value);
    }

    printf("\\n✨ 代码执行完成!\\n");
    return 0;
}`,

        cpp: `// 🚀 欢迎使用在线代码执行平台
// 这里是一个精美的C++示例

#include <iostream>
#include <cmath>
#include <iomanip>

void welcomeMessage() {
    std::cout << std::string(50, '=') << std::endl;
    std::cout << "🎉 欢迎使用代码执行平台!" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

double calculateCircleArea(double radius) {
    return M_PI * radius * radius;
}

int fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

int main() {
    // 显示欢迎信息
    welcomeMessage();

    // 计算圆的面积
    double radius = 5.0;
    double area = calculateCircleArea(radius);
    std::cout << "📐 半径为 " << radius << " 的圆的面积是: "
              << std::fixed << std::setprecision(2) << area << std::endl;

    // 计算斐波那契数列
    std::cout << "\\n🔢 斐波那契数列前10项:" << std::endl;
    for (int i = 0; i < 10; i++) {
        int fibValue = fibonacci(i);
        std::cout << "F(" << i << ") = " << fibValue << std::endl;
    }

    std::cout << "\\n✨ 代码执行完成!" << std::endl;
    return 0;
}`
      },
      code: '',
      selectedLanguage: 'python',

      // 主题相关
      themes: [
        { name: '浅色', value: 'vs', icon: 'el-icon-sunny' },
        { name: '深色', value: 'vs-dark', icon: 'el-icon-moon' },
        { name: '高对比度', value: 'hc-black', icon: 'el-icon-view' }
      ],
      currentThemeIndex: 1, // 默认深色主题

      // 布局相关
      editorWidth: 70, // 百分比
      resultWidth: 30, // 百分比
      isEditorExpanded: false,
      isFullscreen: false,
      isResizing: false,
      showResultDialog: false,

      // 执行相关
      isExecuting: false,
      executionResult: null
    }
  },
  computed: {
    currentTheme() {
      return this.themes[this.currentThemeIndex]
    },
    editorPanelStyle() {
      if (this.isFullscreen) {
        return { width: '100%' }
      }
      return { width: `${this.editorWidth}%` }
    },
    resultPanelStyle() {
      return { width: `${this.resultWidth}%` }
    }
  },
  mounted() {
    this.loadMonacoEditor()
    this.addResizeListener()
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.dispose()
    }
    this.removeResizeListener()
  },
  methods: {
    // 加载Monaco编辑器
    loadMonacoEditor() {
      this.initMonacoEditor()
    },

    // 初始化Monaco编辑器
    initMonacoEditor() {
      // 获取当前语言配置
      const langConfig = this.supportedLanguages.find(lang => lang.value === this.selectedLanguage);
      const monacoLanguage = langConfig ? langConfig.monacoLang : this.selectedLanguage;

      // 设置初始代码
      this.code = this.languageTemplates[this.selectedLanguage] || '';

      this.editorConfig = {
        value: this.code,
        language: monacoLanguage,
        fixedOverflowWidgets: true,
        theme: this.currentTheme.value,
        fontSize: 14,
        fontFamily: "'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace",
        fontLigatures: true,
        minimap: {enabled: true},
        scrollBeyondLastLine: false,
        automaticLayout: true,
        ...this.getLanguageSpecificOptions(this.selectedLanguage),
        lineNumbers: 'on',
        glyphMargin: true,
        folding: true,
        lineDecorationsWidth: 10,
        lineNumbersMinChars: 3,
        renderWhitespace: 'selection',
        cursorBlinking: 'smooth',
        cursorSmoothCaretAnimation: true,
        smoothScrolling: true,
        scrollbar: {
          vertical: 'visible',
          horizontal: 'visible',
          useShadows: false,
          verticalHasArrows: false,
          horizontalHasArrows: false,
          verticalScrollbarSize: 14,
          horizontalScrollbarSize: 14,
          arrowSize: 11
        },
        mouseWheelScrollSensitivity: 1,
        fastScrollSensitivity: 5
      }

      this.editor = monaco.editor.create(document.getElementById('editorContainer'), this.editorConfig);

      // 确保编辑器可以获得焦点
      this.editor.focus()

      // 强制重新布局
      setTimeout(() => {
        this.editor.layout()
      }, 100)
    },

    // 切换主题
    changeTheme() {
      this.currentThemeIndex = (this.currentThemeIndex + 1) % this.themes.length
      if (this.editor) {
        monaco.editor.setTheme(this.currentTheme.value)
      }
    },

    // 处理语言切换
    handleLanguageChange(newLanguage) {
      console.log('切换语言到:', newLanguage);

      // 更新代码内容为新语言的模板
      this.code = this.languageTemplates[newLanguage] || '';

      // 获取对应的Monaco语言配置
      const langConfig = this.supportedLanguages.find(lang => lang.value === newLanguage);
      const monacoLanguage = langConfig ? langConfig.monacoLang : newLanguage;

      // 更新编辑器配置
      if (this.editor) {
        // 更新编辑器内容
        this.editor.setValue(this.code);

        // 更新语言模式
        const model = this.editor.getModel();
        if (model) {
          monaco.editor.setModelLanguage(model, monacoLanguage);
        }

        // 更新编辑器选项（如果需要特定语言的配置）
        this.editor.updateOptions(this.getLanguageSpecificOptions(newLanguage));
      }
    },

    // 获取特定语言的编辑器配置
    getLanguageSpecificOptions(language) {
      const baseOptions = {
        tabSize: 4,
        insertSpaces: true,
        wordWrap: 'on'
      };

      // 根据不同语言返回特定配置
      switch (language) {
        case 'python':
          return {
            ...baseOptions,
            tabSize: 4,
            insertSpaces: true
          };
        case 'javascript':
          return {
            ...baseOptions,
            tabSize: 2,
            insertSpaces: true
          };
        case 'java':
        case 'c':
        case 'cpp':
          return {
            ...baseOptions,
            tabSize: 4,
            insertSpaces: true
          };
        default:
          return baseOptions;
      }
    },
    // 获取短容器ID
    getShortContainerId(containerId) {
      if (!containerId || containerId.length <= 12) {
        return containerId;
      }
      return `${containerId.substring(0, 12)}...${containerId.substring(containerId.length - 12)}`;
    },
    // 切换全屏模式
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.$nextTick(() => {
        if (this.editor) {
          this.editor.layout()
        }
      })
    },

    // 切换编辑器大小
    toggleEditorSize() {
      this.isEditorExpanded = !this.isEditorExpanded
      if (this.isEditorExpanded) {
        this.editorWidth = 85
        this.resultWidth = 15
      } else {
        this.editorWidth = 70
        this.resultWidth = 30
      }

      this.$nextTick(() => {
        if (this.editor) {
          this.editor.layout()
        }
      })
    },

    // 开始调整大小
    startResize(e) {
      this.isResizing = true
      document.addEventListener('mousemove', this.handleResize)
      document.addEventListener('mouseup', this.stopResize)
      e.preventDefault()
    },

    // 处理调整大小
    handleResize(e) {
      if (!this.isResizing) return

      const container = this.$el.querySelector('.main-content')
      const rect = container.getBoundingClientRect()
      const percentage = ((e.clientX - rect.left) / rect.width) * 100

      if (percentage >= 30 && percentage <= 85) {
        this.editorWidth = percentage
        this.resultWidth = 100 - percentage

        this.$nextTick(() => {
          if (this.editor) {
            this.editor.layout()
          }
        })
      }
    },

    // 停止调整大小
    stopResize() {
      this.isResizing = false
      document.removeEventListener('mousemove', this.handleResize)
      document.removeEventListener('mouseup', this.stopResize)
    },

    // 添加窗口大小监听
    addResizeListener() {
      window.addEventListener('resize', this.handleWindowResize)
    },

    // 移除窗口大小监听
    removeResizeListener() {
      window.removeEventListener('resize', this.handleWindowResize)
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.editor) {
        setTimeout(() => {
          this.editor.layout()
        }, 100)
      }
    },

    // 执行代码
    async executeCode() {
      if (!this.code.trim()) {
        this.$message.warning('请输入要执行的代码')
        return
      }

      this.isExecuting = true
      this.executionResult = null

      try {
        const editorCode = this.editor ? this.editor.getValue() : this.code

        // Base64编码代码，避免传输中的转义问题
        const utf8Bytes = new TextEncoder().encode(editorCode)
        const encodedCode = btoa(String.fromCharCode(...utf8Bytes))

        const response = await executeCode({
          language: this.selectedLanguage,
          code: encodedCode,
          base64: true
        })

        if (response.code === 200) {
          this.executionResult = response.data

          // 全屏模式下显示结果对话框
          if (this.isFullscreen) {
            this.showResultDialog = true
          }
        } else {
          this.$message.error(response.msg || '代码执行失败')
        }
      } catch (error) {
        console.error('代码执行错误:', error)
        this.$message.error('代码执行失败')
      } finally {
        this.isExecuting = false
      }
    },

    // 复制输出内容
    copyOutput(content) {
      navigator.clipboard.writeText(content).then(() => {
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 清空代码
    clearCode() {
      this.code = ''
      if (this.editor) {
        this.editor.setValue('')
      }
    },

    // 重置代码
    resetCode() {
      const defaultCode = this.languageTemplates[this.selectedLanguage] || '';
      this.code = defaultCode;
      if (this.editor) {
        this.editor.setValue(defaultCode);
      }
    },

    // 清空结果
    clearResult() {
      this.executionResult = null
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'SUCCESS': '执行成功',
        'ERROR': '执行失败',
        'TIMEOUT': '执行超时',
        'RUNNING': '执行中'
      }
      return statusMap[status] || status
    },

    // 格式化输出
    formatOutput(output) {
      if (!output) return ''
      return output

        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#x27;/g, "'")
        .trim()
    }
  }
}
</script>

<style scoped lang="scss">
// 现代化设计变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$error-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
$warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

$glass-bg: rgba(255, 255, 255, 0.1);
$glass-border: rgba(255, 255, 255, 0.2);
$glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);

$dark-bg: #0f0f23;
$dark-panel: #1a1a2e;
$dark-card: #16213e;

$border-radius: 16px;
$small-radius: 8px;
$transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
$fast-transition: all 0.2s ease;

// 混合器
@mixin glass-effect {
  background: $glass-bg;
  backdrop-filter: blur(20px);
  border: 1px solid $glass-border;
  box-shadow: $glass-shadow;
}

@mixin modern-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: $transition;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }
}

@mixin button-style($bg, $hover-bg: null) {
  background: $bg;
  border: none;
  border-radius: $small-radius;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  transition: $transition;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

    &::before {
      left: 100%;
    }

    @if $hover-bg {
      background: $hover-bg;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

.code-execution-container {
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;

  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    height: 100vh;
    background: $dark-bg;
  }

  .main-content {
    display: flex;
    gap: 20px;
    padding: 20px;
    min-height: calc(100vh - 160px);
    position: relative;
    z-index: 1;

    .editor-panel {
      @include modern-card;
      display: flex;
      flex-direction: column;
      transition: $transition;
      overflow: hidden;

      &.fullscreen {
        height: calc(100vh - 40px);
        border-radius: $border-radius;
      }

      .panel-header {
        background: $primary-gradient;
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;

        // 装饰性元素
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 16px;

          .header-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);

            i {
              font-size: 20px;
              color: white;
            }
          }

          .header-title {
            .title {
              display: block;
              color: white;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 2px;
            }

            .subtitle {
              display: block;
              color: rgba(255, 255, 255, 0.8);
              font-size: 12px;
              text-transform: uppercase;
              letter-spacing: 1px;
            }
          }
        }

        .editor-controls {
          .control-group {
            display: flex;
            gap: 12px;
            align-items: center;

            .language-select {
              width: 140px;

              ::v-deep .el-input__inner {
                @include glass-effect;
                color: white;
                border-radius: $small-radius;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                }
              }
            }

            .glass-btn {
              @include glass-effect;
              color: white;
              border-radius: $small-radius;
              padding: 8px 16px;
              font-weight: 500;
              transition: $transition;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
              }

              i {
                margin-right: 6px;
              }
            }
          }
        }
      }

      .editor-container {
        flex: 1;
        position: relative;
        min-height: 500px;

        .editor-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
          color: #666;

          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }

        // Monaco编辑器样式修复
        ::v-deep .monaco-editor {
          .overflow-guard {
            position: relative !important;
          }

          .monaco-scrollable-element {
            position: relative !important;
          }

          .monaco-editor-background {
            background-color: transparent;
          }

          .margin {
            background-color: transparent;
          }

          .decorationsOverviewRuler {
            display: block !important;
          }

          .monaco-scrollable-element > .scrollbar {
            z-index: 10 !important;
          }

          .monaco-scrollable-element > .scrollbar > .slider {
            background: rgba(121, 121, 121, 0.4) !important;
            border-radius: 4px !important;
          }

          .monaco-scrollable-element > .scrollbar:hover > .slider {
            background: rgba(100, 100, 100, 0.7) !important;
          }
        }
      }
    }

    .resize-handle {
      width: 8px;
      background: rgba(255, 255, 255, 0.1);
      cursor: col-resize;
      transition: $transition;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &:hover,
      &.resizing {
        background: rgba(255, 255, 255, 0.3);
        width: 12px;
      }

      .resize-indicator {
        position: absolute;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        padding: 8px;
        opacity: 0;
        transition: $transition;

        .resize-dots {
          display: flex;
          flex-direction: column;
          gap: 3px;

          span {
            width: 4px;
            height: 4px;
            background: #667eea;
            border-radius: 50%;
          }
        }
      }

      &:hover .resize-indicator {
        opacity: 1;
      }
    }

    .result-panel {
      @include modern-card;
      display: flex;
      flex-direction: column;
      transition: $transition;
      overflow: hidden;
      .output-container {
        // 限制最大高度，根据实际情况调整
        max-height: calc(100vh - 240px);
        overflow-y: auto; // 超出最大高度时显示垂直滚动条
        padding: 16px;
      }
      .panel-header {
        background: $success-gradient;
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 16px;

          .result-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);

            i {
              font-size: 20px;
              color: white;
            }
          }

          .header-title {
            .title {
              display: block;
              color: white;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 2px;
            }

            .subtitle {
              display: block;
              color: rgba(255, 255, 255, 0.8);
              font-size: 12px;
              text-transform: uppercase;
              letter-spacing: 1px;
            }
          }
        }

        .result-controls {
          .control-group {
            display: flex;
            gap: 12px;
            align-items: center;

            .primary-btn {
              @include button-style($primary-gradient);
              font-weight: 600;

              i {
                margin-right: 6px;
              }
            }

            .glass-btn {
              @include glass-effect;
              color: white;
              border-radius: $small-radius;
              padding: 8px 12px;
              transition: $transition;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
              }
            }
          }
        }
      }

      .execution-status {
        padding: 20px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .status-cards {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          .status-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: $fast-transition;
            flex: 1;
            min-width: 120px;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            }

            .status-icon {
              width: 40px;
              height: 40px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              justify-content: center;

              &.success {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
              }

              &.error {
                background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
              }

              &.time {
                background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
              }

              &.code {
                background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
              }

              i {
                color: white;
                font-size: 16px;
              }
            }

            .status-info {
              flex: 1;

              .status-label {
                display: block;
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
              }

              .status-value {
                display: block;
                font-size: 14px;
                font-weight: 600;
                color: #333;
              }
            }
          }
        }
      }

      .output-container {
        flex: 1;
        overflow-y: auto;

        .output-section {
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);

          &:last-child {
            border-bottom: none;
          }

          .output-header {
            padding: 16px 24px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);

            .output-title {
              display: flex;
              align-items: center;
              gap: 8px;
              font-weight: 600;
              font-size: 14px;
            }

            &.success .output-title {
              color: #52c41a;
            }

            &.error .output-title {
              color: #ff4d4f;
            }

            .copy-btn {
              color: #666;
              transition: $fast-transition;

              &:hover {
                color: #1890ff;
                transform: scale(1.05);
              }

              i {
                margin-right: 4px;
              }
            }
          }

          .output-content-wrapper {
            background: #f8f9fa;

            .output-content {
              margin: 0;
              padding: 20px 24px;
              font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
              font-size: 13px;
              line-height: 1.6;
              white-space: pre-wrap;
              word-break: break-word;
              border: none;
              background: transparent;

              &.success {
                color: #237804;
                background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
              }

              &.error {
                color: #a8071a;
                background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
              }
            }
          }
        }

        .empty-result {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          position: relative;

          .empty-illustration {
            position: relative;
            margin-bottom: 32px;

            .empty-icon {
              width: 80px;
              height: 80px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 20px;
              position: relative;
              z-index: 2;

              i {
                font-size: 32px;
                color: white;
              }
            }

            .empty-waves {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);

              .wave {
                position: absolute;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 50%;
                animation: wave 2s ease-in-out infinite;

                &:nth-child(1) {
                  width: 100px;
                  height: 100px;
                  margin: -50px 0 0 -50px;
                  animation-delay: 0s;
                }

                &:nth-child(2) {
                  width: 140px;
                  height: 140px;
                  margin: -70px 0 0 -70px;
                  animation-delay: 0.5s;
                }

                &:nth-child(3) {
                  width: 180px;
                  height: 180px;
                  margin: -90px 0 0 -90px;
                  animation-delay: 1s;
                }
              }
            }
          }

          .empty-content {
            .empty-title {
              font-size: 24px;
              font-weight: 600;
              color: #333;
              margin-bottom: 12px;
            }

            .empty-text {
              font-size: 16px;
              color: #666;
              margin-bottom: 8px;
            }

            .empty-hint {
              font-size: 14px;
              color: #999;
            }
          }
        }

        .executing-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          text-align: center;

          .executing-animation {
            position: relative;
            margin-bottom: 32px;

            .code-lines {
              display: flex;
              flex-direction: column;
              gap: 8px;
              margin-bottom: 20px;

              .code-line {
                height: 4px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 2px;
                animation: codeFlow 2s ease-in-out infinite;

                &:nth-child(1) { width: 80px; }
                &:nth-child(2) { width: 120px; }
                &:nth-child(3) { width: 100px; }
                &:nth-child(4) { width: 90px; }
                &:nth-child(5) { width: 110px; }
              }
            }

            .executing-spinner {
              position: relative;
              width: 60px;
              height: 60px;
              margin: 0 auto;

              .spinner-ring {
                position: absolute;
                border: 3px solid transparent;
                border-radius: 50%;
                animation: spin 2s linear infinite;

                &:nth-child(1) {
                  width: 60px;
                  height: 60px;
                  border-top-color: #667eea;
                  animation-delay: 0s;
                }

                &:nth-child(2) {
                  width: 45px;
                  height: 45px;
                  top: 7.5px;
                  left: 7.5px;
                  border-top-color: #764ba2;
                  animation-delay: 0.3s;
                  animation-direction: reverse;
                }

                &:nth-child(3) {
                  width: 30px;
                  height: 30px;
                  top: 15px;
                  left: 15px;
                  border-top-color: #4facfe;
                  animation-delay: 0.6s;
                }
              }
            }
          }

          .executing-content {
            .executing-title {
              font-size: 20px;
              font-weight: 600;
              color: #667eea;
              margin-bottom: 8px;
            }

            .executing-text {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }
    }
  }

  // 全屏模式下的浮动控制面板
  .fullscreen-controls {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 10000;

    .floating-panel {
      @include glass-effect;
      padding: 20px;
      border-radius: $border-radius;
      display: flex;
      gap: 16px;

      .primary-btn {
        @include button-style($primary-gradient);
        font-weight: 600;

        i {
          margin-right: 8px;
        }
      }

      .glass-btn {
        @include glass-effect;
        color: white;
        border-radius: $small-radius;
        padding: 10px 16px;
        transition: $transition;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        i {
          margin-right: 8px;
        }
      }
    }
  }

  // 结果对话框样式
  ::v-deep .result-dialog {
    .el-dialog {
      border-radius: $border-radius;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      overflow: hidden;
    }

    .el-dialog__header {
      background: $success-gradient;
      color: white;
      padding: 24px;

      .el-dialog__title {
        color: white;
        font-weight: 600;
        font-size: 18px;
      }

      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .dialog-content {
      padding: 24px;

      .execution-status {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .dialog-output {
        margin-bottom: 20px;

        h4 {
          margin-bottom: 12px;
          color: #52c41a;
          font-size: 16px;
          font-weight: 600;
        }

        &.error h4 {
          color: #ff4d4f;
        }

        pre {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.6;
          overflow-x: auto;
          border: 1px solid #e4e7ed;
        }
      }
    }
  }
}

// 下拉菜单样式
::v-deep .custom-dropdown {
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .el-dropdown-menu__item {
    padding: 12px 20px;
    transition: $fast-transition;

    &:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      color: #667eea;
    }

    i {
      margin-right: 8px;
      width: 16px;
    }
  }
}

// 动画定义
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes wave {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
}

@keyframes codeFlow {
  0%, 100% {
    opacity: 0.3;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .code-execution-container .main-content {
    flex-direction: column;
    gap: 16px;

    .editor-panel {
      width: 100% !important;
      height: 60vh;
    }

    .resize-handle {
      height: 8px;
      width: 100%;
      cursor: row-resize;

      .resize-indicator .resize-dots {
        flex-direction: row;
      }
    }

    .result-panel {
      width: 100% !important;
      height: 40vh;
    }
  }

  .fullscreen-controls {
    bottom: 20px;
    right: 20px;

    .floating-panel {
      padding: 16px;
      gap: 12px;
    }
  }
}

@media (max-width: 768px) {
  .code-execution-container {
    .main-content {
      padding: 16px;
      gap: 12px;
    }

    .panel-header {
      padding: 16px 20px !important;

      .header-left {
        gap: 12px !important;

        .header-icon {
          width: 40px !important;
          height: 40px !important;

          i {
            font-size: 16px !important;
          }
        }

        .header-title .title {
          font-size: 16px !important;
        }
      }

      .control-group {
        gap: 8px !important;

        .glass-btn {
          padding: 6px 12px !important;
          font-size: 12px !important;
        }
      }
    }
    .fullscreen-controls {
      bottom: 16px;
      right: 16px;

      .floating-panel {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
      }
    }
  }
}
</style>
