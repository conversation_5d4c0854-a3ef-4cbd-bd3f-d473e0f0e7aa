<template>
  <div
    class="lesson-plan-demo"
    style="display: flex; flex-direction: column; gap: 20px; min-height: 100vh;"
    v-loading="loading"
    element-loading-text="正在生成教案，请稍候..."
    element-loading-fullscreen
    element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <!-- 上边的配置区域 -->
    <!-- 修改宽度为 100%，使其占满上部分宽度 -->
    <el-card class="config-panel" style="width: 100%;">
      <h3>教案配置</h3>
      <el-form :model="form" label-width="100px">

        <!-- 新增：教学课程下拉选择框 -->
        <el-form-item label="教学课程">
          <el-select v-model="form.course" placeholder="请选择教学课程">
            <el-option
              v-for="course in courseOptions"
              :key="course"
              :label="course"
              :value="course"
            :disabled="course !== 'C语言'"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="教学单元">
          <el-select v-model="form.outline" placeholder="请选择教学单元">
            <el-option
                v-for="item in allOutlines"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>

<!--        <el-form-item label="学情分析">-->
<!--          <el-alert-->
<!--              title="本班学生基础一般，需加强动手训练，部分学生对编程语法掌握较弱。"-->
<!--              type="info"-->
<!--              show-icon-->
<!--          />-->
<!--        </el-form-item>-->

<!--        <el-form-item label="教师偏好">-->
<!--          <el-input type="textarea" v-model="form.preference" placeholder="如：注重互动、小组讨论..." />-->
<!--        </el-form-item>-->

        <el-form-item label="知识点" v-if="form.outline && knowledgeMap[form.outline]">
          <!-- 知识点选择卡片容器 -->
          <div class="knowledge-container">
            <!-- 操作按钮区域 - 修复按钮拉满问题 -->
            <div class="knowledge-buttons">
              <el-button
                size="mini"
                type="primary"
                @click="selectAllKnowledge"
                class="knowledge-btn"
              >全选</el-button>
              <el-button
                size="mini"
                type="warning"
                @click="clearKnowledge"
                class="knowledge-btn"
              >清空</el-button>
              <el-button
                size="mini"
                type="info"
                @click="invertKnowledge"
                class="knowledge-btn"
              >反选</el-button>
            </div>

            <!-- 复选框列表（优化版）-->
            <el-checkbox-group v-model="form.knowledgePoints" class="knowledge-list">
              <el-checkbox
                v-for="item in knowledgeMap[form.outline]"
                :key="item"
                :label="item"
                class="knowledge-item"
              >
              {{ item }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>


        <el-form-item label="教学目标">
          <el-select v-model="form.level" placeholder="请选择">
            <el-option label="基础" value="基础" />
            <el-option label="进阶" value="进阶" />
            <el-option label="拓展" value="拓展" />
          </el-select>
        </el-form-item>

<!--        <el-form-item label="教学方法">-->
<!--          <el-select v-model="form.methods" multiple placeholder="请选择教学方法">-->
<!--            <el-option label="探究式" value="探究式" />-->
<!--            <el-option label="翻转课堂" value="翻转课堂" />-->
<!--            <el-option label="小组合作" value="小组合作" />-->
<!--            <el-option label="任务驱动" value="任务驱动" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->

        <el-form-item>
          <el-button type="primary" @click="generateLessonPlan">生成教案</el-button>
          <!-- 导出按钮 - 移至此处，使用 showPreview 控制显示 -->
          <el-button
            type="success"
            @click="downloadDoc"
            :disabled="!lesson"
            style="margin-left: 10px;"
            v-if="showPreview"
          >
            导出为 Word
          </el-button>
        </el-form-item>

      </el-form>
    </el-card>

    <!-- 下边的教案预览 -->
    <el-card
      class="preview-panel"
      style=""
      v-if="showPreview"
    >
      <h3>生成教案预览</h3>

      <!-- 加 v-if 判断 lesson 有值再展示 -->
      <div v-if="lesson" ref="lessonContent">
        <p><strong>
          {{ allOutlines.find(item => item.value === form.outline)
            ? allOutlines.find(item => item.value === form.outline).label
            : ''
          }}</strong><br />
        </p>
        <p><strong>教学目标：</strong><br />{{ lesson.goal }}</p>
        <p><strong>教学重点：</strong><br />{{ lesson.keyPoints }}</p>
        <p><strong>教学难点：</strong><br />{{ lesson.difficulties }}</p>
        <p><strong>教学过程：</strong><br />{{ lesson.process }}</p>
        <p><strong>教学活动：</strong><br />{{ lesson.activities }}</p>
        <p><strong>作业安排：</strong><br />{{ lesson.homework }}</p>
      </div>

      <!-- 没有 lesson 时提示一下 -->
      <div v-else style="margin: 20px 0; color: gray; font-style: italic;">
        暂无教案内容，请在左侧选择教学单元并点击“生成教案”
      </div>

    </el-card>
  </div>
</template>

<script>
import { saveAs } from 'file-saver'
// import createDocx from 'html-docx-js/dist/html-docx'

export default {
  name: 'LessonPlanDemo',
  data() {
    return {
      form: {
        course: 'C语言',
        outline: '',
        level: '',
        methods: [],
        chapter: '',
        knowledgePoints: [],
        knowledge: '',
        preference: ''
      },
      loading: false,
      showPreview: false,
      lesson: {
        goal: '',
        keyPoints: '',
        difficulties: '',
        process: '',
        activities: '',
        homework: ''
      },
      allOutlines: [
        { label: '第一单元 初识C语言', value: 'unit1', disabled: true },
        { label: '第二单元 C语言基础', value: 'unit2', disabled: true },
        { label: '第三单元 顺序结构', value: 'unit3', disabled: false },
        { label: '第四单元 选择结构', value: 'unit4', disabled: false },
        { label: '第五单元 循环结构', value: 'unit5', disabled: false },
        { label: '第六单元 数组', value: 'unit6', disabled: true },
        { label: '第七单元 函数', value: 'unit7', disabled: true },
        { label: '第八单元 指针', value: 'unit8', disabled: true },
        { label: '第九单元 结构体和共用体', value: 'unit9', disabled: true },
        { label: '第十单元 文件', value: 'unit10', disabled: true },
        { label: '第十一单元 综合项目实训', value: 'unit11', disabled: true }
      ],
      knowledgeMap: {
        unit3: [
          '顺序结构基本概念',
          '顺序结构与程序执行流程',
          'C语言基本语句格式',
          '变量定义与初始化',
          '数据输入：scanf函数',
          '数据输出：printf函数',
          '字符输入输出',
          '转义字符的使用（如\\n、\\t）',
          '格式化字符串的写法',
          '常用格式控制符',
          '顺序结构的代码块规范',
          'C语言语句结束符“;”的使用',
          '算法与C语言的映射关系',
          '简单数学计算程序设计',
          '程序调试与错误类型识别',
          '变量命名规范及常见问题',
          '使用注释提高程序可读性',
          '编写用户交互式程序',
          '设计一个输入输出流程图'
        ],
        unit4: [
          '关系运算符（> < >= <= == !=）',
          '关系表达式与布尔值结果',
          '逻辑运算符（&& || !）使用方法',
          '逻辑表达式优先级及结合性',
          'if单分支语句结构',
          'if-else双分支语句结构',
          '多重if-else语句嵌套',
          '条件运算符(?:)的使用',
          'switch语句结构及执行流程',
          'switch中break的必要性',
          'default分支的作用与可选性',
          '多条件判断的设计方法',
          '选择结构与代码可读性提升',
          '输入判断合法性校验案例',
          '调试选择结构中的逻辑错误',
          '代码缩进规范对选择结构的影响',
          '嵌套if语句与逻辑混乱问题',
          '选择结构与流程图表示'
        ],
        unit5: [
          '循环结构的基本概念',
          'while循环的语法与执行流程',
          'do-while循环的特点与适用场景',
          'for循环三要素及规范写法',
          '循环体的控制与语句块设计',
          'break语句终止循环执行',
          'continue语句跳过当前迭代',
          '循环计数器变量的正确使用',
          '嵌套循环结构的设计与调试',
          '循环条件的布尔逻辑分析',
          '死循环常见原因与处理',
          'for与while循环的功能比较',
          '常见应用场景：求和/阶乘/遍历数组',
          '使用循环绘制控制台图案',
          '用户输入驱动的循环（如菜单系统）',
          '循环中格式化输出的对齐技巧',
          '输入数据合法性判断中的循环应用',
          '循环变量溢出风险与边界控制',
          '项目实战：计算质数/水仙花数'
        ]
      },
      lessonTemplates: {
        unit3: {
          goal: `知识目标：
1. 理解顺序结构的基本概念及其在程序设计中的重要性。
2. 掌握C语言中常用的输入输出函数的使用方法。
3. 了解算法与顺序结构的关系，学会将简单算法转化为C语言程序。

能力目标：
1. 能识别并解释C语言程序中的顺序结构。
2. 能熟练使用scanf()、printf()、getchar()、putchar()等函数进行数据的输入输出。
3. 能将简单算法转化为顺序结构的C语言程序，并调试运行。

素质目标：
1. 培养逻辑思维与问题解决能力，学会运用所学知识分析和解决计算机程序设计中的实际问题。
2. 增强自主学习与创新意识，鼓励主动探索、实践和创新。`,
          keyPoints: `1. 顺序结构的基本概念及其在C语言程序中的应用。
2. scanf()、printf()、getchar()、putchar()等输入输出函数的使用方法。
3. 将简单算法转化为顺序结构的C语言程序。`,
          difficulties: `1. 理解顺序结构在复杂程序中的应用，尤其是在多步骤数据处理中的组织。
2. 掌握scanf()、printf()等格式化输入输出函数的格式控制字符串的使用。
3. 将算法准确无误地转化为C语言程序，并调试通过。`,
          process: `课前准备：
1. 准备教学资料，包括顺序结构的概念、C语言输入输出函数的示例代码。
2. 在头歌平台建立班课，布置预习任务，如了解顺序结构的基本概念。

课中：
1. 头歌平台小测试（15分钟）：包括二进制和十进制的转换等问题。
2. 顺序结构概念讲解（10分钟）。
3. 输入输出函数讲解（20分钟）。
4. 实例编程与操作（30分钟）：两个实例程序。
5. 互动问答与讨论（15分钟）。

课后：
1. 布置作业1：输入一个浮点数输出平方。
2. 布置作业2：统计字符串中字母'a'出现次数。`,
          activities: `1. 小组讨论：探究顺序结构在复杂程序中的应用。
2. 编程竞赛：实践中掌握顺序结构的使用。`,
          homework: `1. 输入三角形三边，计算面积。
2. 输入5位整数，输出每一位的值。`
        },
        unit4: {
          goal: `知识目标：
1. 理解关系运算符和关系表达式的概念及优先级。
2. 掌握逻辑运算符和逻辑表达式的构成及运算规则。
3. 熟悉if选择语句（单分支、双分支、多分支）的语法结构。
4. 理解条件运算符的语法，并能进行简单的条件判断。
5. 掌握switch多分支语句的语法结构及应用。

能力目标：
1. 能根据具体需求选择合适的关系运算符和逻辑运算符构建关系表达式和逻辑表达式。
2. 能根据条件判断需求，设计并实现单分支、双分支及多分支if语句。
3. 能使用条件运算符进行简单的条件判断，并理解其与if-else语句的功能对比。
4. 能根据条件选择需求，设计并实现switch多分支语句。
5. 能通过案例分析、小组讨论和动手实践，加深对选择结构的理解和应用。

素质目标：
1. 培养逻辑思维与问题解决能力：通过学习C语言选择结构，建立起逻辑思维体系，提高解决实际问题的能力。
2. 增强自主学习与创新意识：鼓励学生主动探索、实践和创新，培养其自主学习的习惯和能力，以及对新技术、新知识的敏感度和求知欲。`,
          keyPoints: `1. 关系运算符和关系表达式的优先级与结合性。
2. 逻辑运算符和逻辑表达式的构成与运算。
3. if选择语句的多种形式及其执行过程。
4. switch多分支语句的语法结构与执行过程。`,
          difficulties: `1. C语言严格的语法规则：
- 确保变量在使用前先声明后使用，避免因未定义变量导致的问题。
2. 运算符的优先级和结合性：
- 符号数、算术、符、逻辑与位等运算符的优先级，避免类型转换和溢出等问题。
3. printf和scanf等函数的格式化字符串语法：
- 确保不同类型的数据按照指定格式正确输出，避免因格式不匹配导致的问题。`,
          process: `课前准备：
- 在课程开始前，确保所有教学设备和环境处于良好状态。

课中：
- 头歌平台小测试：进行二进制和十进制的转换、机内码表示等测试。
- 讲解与演示：实例演示关系运算符、逻辑运算符、if选择语句和switch语句。
- 互动问答：设置课堂提问，解答学生疑问，加深理解。

课后：
- 布置作业：
1. 输入三角形三边，输出其个十百位；
2. 输入一个5位整数，求其各位数值；
3. 输入两个整数，输出最大数；
4. 输入一个整数，判断其正负性。`,
          activities: `1. 小组讨论：探讨关系运算符、逻辑运算符及其适用场景。
2. 动手实践：编写使用选择结构的简单程序，加深理解。`,
          homework: `1. 输入两个整数，输出最大值；
2. 输入一个整数，判断正负；
3. 计算三角形面积；
4. 分解5位整数，并输出各位数字。`
        },
        unit5: {
          goal: `知识目标：
1. 理解循环结构的基本概念及其在程序设计中的应用。
2. 掌握while、do-while、for三种循环语句的语法结构及执行过程。
3. 通过实例分析，理解循环结构在实际编程中的应用。

能力目标：
1. 能够使用while、do-while、for语句编写简单的循环控制程序。
2. 能够设计并实现包含循环嵌套的复杂程序。
3. 能够使用break与continue语句有效控制循环执行流程。

素质目标：
1. 培养逻辑思维，通过学习循环结构，建立清晰的程序执行逻辑，提升问题解决能力。
2. 增强实践能力，通过动手实践，加深对循环结构的理解和应用能力。`,
          keyPoints: `1. 循环语句的语法：强调循环语句的语法结构及其执行流程。
2. 循环结构的逻辑设计：理解循环结构在程序设计中的逻辑关系，掌握循环控制条件的描述方法。
3. 循环嵌套的实现：理解循环嵌套的概念，掌握多重循环的设计与实现技巧。`,
          difficulties: `1. 循环条件的准确描述：学生可能难以准确描述复杂的循环条件。
2. 循环体的合理设计：在循环体内合理设置循环控制变量和终止条件，避免死循环。
3. 嵌套循环的理解：学生可能难以理解和实现复杂的循环嵌套结构。`,
          process: `课前准备：
- 建立头歌平台班课，准备教学资料，包括PPT、教案、示例代码等。
- 分配预习任务，让学生提前了解循环结构的基本概念和应用场景。

课中：
- 小测试：通过头歌平台进行循环结构相关的小测试。
- 理论讲授：结合PPT和示例代码，讲解循环结构的基本概念、语法结构及执行流程。
- 实例演示：编程实例演示循环结构在实际编程中的应用。
- 小组讨论：讨论循环结构的应用场景与注意事项。
- 动手实践：学生动手编写包含循环结构的程序。

课后：
- 平台作业：布置相关作业，巩固所学知识。
- 答疑辅导：在线答疑，解决学生在写程序中遇到的问题。`,
          activities: `1. 案例分析：分析循环结构在典型程序中的作用。
2. 小组竞赛：组织学生编写包含复杂循环结构的程序。
3. 项目实践：设计小项目，通过循环结构实现特定功能。`,
          homework: `1. 基础作业：编写while、do-while、for循环程序，输出特定图案或结果。
2. 综合应用：编写包含循环嵌套的程序，如阶乘、水仙花数等。
3. 拓展创新：结合实际案例，设计创新性的循环结构解决方案。`
        },
      },
      courseOptions: [
        'C语言',
        'Java编程',
        'Python数据分析',
        'JavaScript前端开发',
        'C++面向对象编程',
        '数据结构与算法',
        '数据库原理与应用',
        '操作系统',
        '计算机网络',
        '人工智能导论'
      ],
    }


  },
  methods: {
    generateLessonPlan() {
      // 新增：表单验证逻辑
      if (!this.form.outline) {
        this.$message.warning('请选择教学大纲');
        return;
      }

      if (!this.form.level) {
        this.$message.warning('请选择目标复杂度');
        return;
      }

      // if (!this.form.methods || this.form.methods.length === 0) {
      //   this.$message.warning('请选择至少一种教学方法');
      //   return;
      // }

      if (this.knowledgeMap[this.form.outline] && this.form.knowledgePoints.length === 0) {
        this.$message.warning('请至少选择一个知识点');
        return;
      }

      const key = this.form.outline;
      if (!key || !this.lessonTemplates[key]) {
        this.$message.warning('请选择支持的教学单元');
        return;
      }

      this.loading = true;
      this.lesson = null; // 清空旧内容

      // 模拟生成延迟
      setTimeout(() => {
        this.lesson = this.lessonTemplates[key];
        this.loading = false;
        this.showPreview = true;
      }, 1500); // 可调整成 1000~2000ms
    },
    downloadDoc() {
      const map = {
        unit3: '/lesson-plans/教案_顺序结构.docx',
        unit4: '/lesson-plans/教案_选择结构.docx',
        unit5: '/lesson-plans/教案_循环结构.docx'
      }
      const selected = this.form.outline

      if (!selected || !map[selected]) {
        this.$message.warning('请选择支持的教学单元（第3~5单元）')
        return
      }

      const fileUrl = map[selected]
      const filename = this.allOutlines.find(i => i.value === selected)?.label + '.docx'

      fetch(fileUrl)
        .then(response => {
          if (!response.ok) throw new Error('下载失败')
          return response.blob()
        })
        .then(blob => {
          saveAs(blob, filename)
        })
        .catch(err => {
          console.error('下载错误：', err)
          this.$message.error('Word 文件下载失败')
        })
    },
    // 全选
    selectAllKnowledge() {
      this.form.knowledgePoints = [...this.knowledgeMap[this.form.outline]];
    },
    // 清空
    clearKnowledge() {
      this.form.knowledgePoints = [];
    },
    // 反选
    invertKnowledge() {
      const current = this.form.knowledgePoints || [];
      const all = this.knowledgeMap[this.form.outline];
      this.form.knowledgePoints = all.filter(i => !current.includes(i));
    },
  }
}
</script>

<style scoped>
.el-card {
  padding: 20px;
}
/* 知识点选择区域样式 */
.knowledge-container {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9fafc;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

/* 按钮区域样式 */
.knowledge-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e4e7ed;
  justify-content: flex-start;
}

.knowledge-btn {

  transition: all 0.3s ease;
}

/* 复选框列表样式 */
.knowledge-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 复选框项样式 */
.knowledge-item {
  /* 使用固定宽度 */
  width: calc(20% - 10px); /* 5个元素一排 */
  min-width: 150px;
  padding: 10px 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;

  /* 单行文本截断 */
  white-space: nowrap; /* 强制不换行 */
  overflow: hidden;    /* 溢出隐藏 */
  text-overflow: ellipsis; /* 溢出文本用省略号 */

  /* 固定行高确保高度一致 */
  line-height: 1.5;    /* 单行高度 */
  height: 42px;        /* 固定高度值（根据内容调整） */
  display: flex;       /* 使用flex确保垂直居中 */
  align-items: center; /* 文本垂直居中 */
}

.knowledge-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 选中状态样式 */
.knowledge-item.is-checked {
  border-color: #409eff;
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: 500;
}

/* 复选框图标样式调整 */
.knowledge-item .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}



</style>
